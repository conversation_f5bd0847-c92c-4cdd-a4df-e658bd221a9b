package com.lvpuhui.gic.wxapp.homepage.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehavior;
import com.lvpuhui.gic.wxapp.my.entity.GlBusinessPointsLogDO;
import com.lvpuhui.gic.wxapp.other.entity.GlConsumption;
import com.lvpuhui.gic.wxapp.homepage.enums.PointsType;
import com.lvpuhui.gic.wxapp.homepage.entity.*;
import com.lvpuhui.gic.wxapp.my.entity.GlUserFriends;
import com.lvpuhui.gic.wxapp.pointexchange.entity.GlGoods;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 积分流水表(GlPoints)表服务接口
 * 积分分值及兑换：
 * 100积分1块钱，1积分=1分钱
 * 1g=0.001元=0.1积分
 * 100块钱的消费产生100积分，相当于奖励了1块钱
 * 5000块消费产生5000积分，相当于奖励了50块钱
 * 1公里自行车=250g   25积分    0.25元
 * 1公里开新能源=115g    12积分   0.12元
 * <AUTHOR>
 * @since 2022-05-06 13:45:10
 */
public interface GlPointsService extends IService<GlPoints> {

    JSONObject getBehaviorMap();

    /**
     * 绿色消费 生成积分
     * @param consumption
     * @param  points
     */
    void grantConsumptionPoints(GlConsumption consumption, double points);


    /**
     * 绿色行为 生成积分
     * @param behavior
     * @param rate
     */
    void grantBehaviorPoints(GlBehavior behavior, double rate);

    /**
     * 绿色行为生成积分
     * @param behavior
     * @param rate
     * @param pointsType
     */
    void grantBehaviorPoints(GlBehavior behavior, double rate, PointsType pointsType);

    void grantBehaviorPoints2(GlBehavior behavior, double rate);
    /**
     * 看视频生成积分
     * @param glVideoRecord
     * @param companyId
     * @param companyName
     */
    void grantVideo(GlVideoRecord glVideoRecord, Long companyId, String companyName, String mobileSha256, Long videoId);

    /**
     * 打卡 生成积分
     * @param tickoff
     * @param companyId
     */
    void grantTickoff(GlTickoff tickoff, Long companyId);
    /**
     * 生成用户积分
     * @param p
     */
    void grantUsrPoints(GlPoints p);

    /**
     * 生成用户积分
     * @param set
     */
    void grantUserPoints(Set<String> set);

    /**
     * 兑换商品 消费积分
     * @param goods
     * @param mobileSha256
     */
    void consumedPoints(GlGoods goods, String mobileSha256);


    /**
     * 计算用户积分 并更新到gl_user
     * @param mobileSha256
     */
    void calcUserPoints(String mobileSha256);

    /**
     * 填写问卷生成积分
     */
    void grantQuestion(GlQuestionAnswerRecords glQuestionAnswerRecords, Long companyId, String companyName, String mobileSha256, Long id);

    /**
     * 答题生成积分
     */
    void grantSubject(GlSubjectRecord glSubjectRecord, Long companyId, String companyName, String mobileSha256);

    /**
     * 排行领取商品生成积分数据
     */
    void rankGoodsPoints(GlGoods goods,String mobileSha256);

    /**
     * 邀请发放积分
     * @param userFriends
     * @param points
     * @param nickname
     */
    void grantInvite(GlUserFriends userFriends, Double points, String nickname);

    void sendUserReadPoints(GlBusinessPointsLogDO glBusinessPointsLogDO, String mobile, LocalDateTime localDateTime);

    void grantBehaviorPoint(GlBehavior glBehavior, double rate);
}