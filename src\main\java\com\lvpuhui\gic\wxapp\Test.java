package com.lvpuhui.gic.wxapp;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @since 2024年05月20日 14:29:00
 */
@Slf4j
public class Test {

    // 正则表达式匹配时间、token和params
    private static final Pattern TIME_PATTERN = Pattern.compile("(\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}\\.\\d{3})");
    private static final Pattern TOKEN_PATTERN = Pattern.compile("token:([a-f0-9]+)");
    private static final Pattern PARAMS_PATTERN = Pattern.compile("params:\\[(\\{[^}]+\\})\\]");

    public static void main(String[] args) {
        String inputFile = "E:/sendUserReadPoints-20250814.log";
        String outputFile = "D:/process.log";

        try {
            processLogFile(inputFile, outputFile);
            log.info("日志处理完成！输出文件：{}", outputFile);
        } catch (Exception e) {
            log.error("处理日志文件失败：", e);
        }
    }

    public static void processLogFile(String inputFile, String outputFile) throws IOException {
        // 读取输入文件所有行
        List<String> lines = FileUtil.readLines(inputFile, StandardCharsets.UTF_8);
        log.info("读取到{}行数据", lines.size());

        // 创建输出文件的BufferedWriter
        try (BufferedWriter writer = FileUtil.getWriter(outputFile, StandardCharsets.UTF_8, false)) {
            int processedCount = 0;
            int skippedCount = 0;

            for (String line : lines) {
                if (StrUtil.isBlank(line)) {
                    continue;
                }

                String processedLine = processLine(line);
                if (StrUtil.isNotBlank(processedLine)) {
                    writer.write(processedLine);
                    writer.newLine();
                    processedCount++;
                } else {
                    skippedCount++;
                    log.debug("跳过无法解析的行：{}", line);
                }
            }

            writer.flush();
            log.info("处理完成：成功处理{}行，跳过{}行", processedCount, skippedCount);
        }
    }

    private static String processLine(String line) {
        try {
            // 提取时间
            String time = extractTime(line);
            if (StrUtil.isBlank(time)) {
                return null;
            }

            // 提取token
            String token = extractToken(line);
            if (StrUtil.isBlank(token)) {
                return null;
            }

            // 提取params中的JSON对象
            String params = extractParams(line);
            if (StrUtil.isBlank(params)) {
                return null;
            }

            // 组合结果：时间,token,params
            return String.format("%s|%s|%s", time, token, params);

        } catch (Exception e) {
            log.warn("解析行数据失败：{}", line, e);
            return null;
        }
    }

    private static String extractTime(String line) {
        Matcher matcher = TIME_PATTERN.matcher(line);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    private static String extractToken(String line) {
        Matcher matcher = TOKEN_PATTERN.matcher(line);
        if (matcher.find()) {
            return matcher.group(1); // 不包含"token:"前缀
        }
        return null;
    }

    private static String extractParams(String line) {
        Matcher matcher = PARAMS_PATTERN.matcher(line);
        if (matcher.find()) {
            return matcher.group(1); // 提取JSON对象部分
        }
        return null;
    }
}
