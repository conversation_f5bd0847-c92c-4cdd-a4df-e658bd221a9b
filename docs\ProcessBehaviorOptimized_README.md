# 优化版绿色行为处理器使用说明

## 概述

优化版绿色行为处理器是对原有 `ProcessBehaviorRunnable` 的重新设计和实现，专门针对大数据量（1亿条左右）的处理场景进行了优化。

## 主要优化点

### 1. 架构优化
- **流水线处理**：数据读取和处理并行进行，提高整体效率
- **队列缓冲**：使用256个队列对应256个分表，避免数据分散导致的性能问题
- **多线程并发**：6个处理线程并行处理不同分表的数据

### 2. 性能优化
- **批量操作**：批量插入和批量积分检查，减少数据库交互次数
- **内存控制**：队列大小限制，避免内存溢出
- **速度控制**：多层次的速度控制，保护数据库不被压垮

### 3. 可靠性优化
- **断点续传**：支持中断后从上次位置继续处理
- **积分防重发**：批量检查积分是否已存在，避免重复发放
- **异常处理**：完善的异常处理和日志记录

## 核心组件

### 1. ProcessBehaviorOptimizedRunnable
主控制器，负责整体流程的协调和管理。

### 2. DataReaderThread
数据读取线程，从 `gl_behavior_tmp_copy1` 表中按ID顺序读取数据，并分发到对应的队列中。

### 3. TableProcessorThread
分表处理线程，从队列中取出数据进行批量处理，包括：
- 批量插入到对应的 `gl_behavior` 分表
- 批量检查积分重复
- 批量发放积分

### 4. TableQueueManager
队列管理器，管理256个分表队列，负责数据的分发和取出。

### 5. ProcessBehaviorProgressManager
进度管理器，负责进度的持久化、监控和断点续传。

## 数据库表结构

### process_behavior_progress
主进度表，记录整体处理进度。

### process_behavior_table_progress
分表进度表，记录每个分表的处理进度。

## API接口

### 启动处理器
```
POST /behavior/optimized/start
```

### 获取处理进度
```
GET /behavior/optimized/progress
```

### 暂停处理
```
POST /behavior/optimized/pause
```

### 恢复处理
```
POST /behavior/optimized/resume
```

### 停止处理
```
POST /behavior/optimized/stop
```

## 配置参数

```java
// 核心配置参数
READ_BATCH_SIZE = 10000;        // 读取批次大小
PROCESS_BATCH_SIZE = 300;       // 处理批次大小
QUEUE_CAPACITY = 1000;          // 队列容量
WRITE_SEMAPHORE_PERMITS = 3;    // 写入并发数
PROCESS_DELAY_MS = 75;          // 处理延迟
THREAD_COUNT = 6;               // 处理线程数
```

## 使用步骤

### 1. 数据库准备
执行 `src/main/resources/sql/process_behavior_progress_tables.sql` 创建进度表。

### 2. 启动处理
调用启动接口开始处理：
```bash
curl -X POST http://localhost:8080/behavior/optimized/start
```

### 3. 监控进度
定期查询处理进度：
```bash
curl -X GET http://localhost:8080/behavior/optimized/progress
```

### 4. 控制处理
根据需要暂停、恢复或停止处理。

## 性能预期

基于6核12线程的服务器配置：
- **处理速度**：预计每秒处理1000-3000条记录
- **内存占用**：约256MB（队列缓存）
- **数据库压力**：通过速度控制和批量操作，对数据库压力可控

## 注意事项

1. **数据库性能**：确保数据库有足够的连接池和性能支持
2. **内存监控**：监控应用内存使用情况
3. **日志监控**：关注处理日志，及时发现异常
4. **断点续传**：如果需要重启，可以从上次中断的位置继续

## 故障排除

### 常见问题

1. **内存不足**：减少队列容量或处理批次大小
2. **数据库连接超时**：增加数据库连接池大小
3. **处理速度慢**：检查数据库性能，调整延迟参数

### 日志关键字

- `数据读取线程启动`：读取线程开始工作
- `分表处理线程 X 启动`：处理线程开始工作
- `队列积压过多`：队列堆积，可能需要调整参数
- `处理完成`：整体处理完成

## 与原版本对比

| 特性 | 原版本 | 优化版本 |
|------|--------|----------|
| 并发度 | 单线程读取 + 线程池处理 | 1个读取线程 + 6个处理线程 |
| 内存使用 | 不可控 | 可控（队列限制） |
| 断点续传 | 不支持 | 支持 |
| 进度监控 | 基础日志 | 详细进度API |
| 积分防重发 | 单条检查 | 批量检查 |
| 数据库压力 | 较高 | 可控 |

优化版本在处理大数据量时具有明显优势，特别适合1亿条左右的数据处理场景。
