package com.lvpuhui.gic.wxapp.my.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.homepage.dto.MyCarbonBookDataVo;
import com.lvpuhui.gic.wxapp.my.dto.*;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.pointexchange.dto.PointData;
import com.lvpuhui.gic.wxapp.pointexchange.dto.PointDataDto;

import javax.servlet.http.HttpServletRequest;

/**
 * 用户积分表(GlUser)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-06 15:16:32
 */
public interface GlUserService extends IService<GlUser> {

    /**
     * 我的绿色积分接口
     */
    PointData pointData(PointDataDto pointDataDto);

    /**
     * 用户授权接口
     */
    UserInfo userAuth(UserAuthDto userAuthDto);

    /**
     * 根据code获取openid和加密手机号接口
     */
    CodeOpenid code2session(CodeOpenidDto codeOpenidDto);

    /**
     * 获取微信token
     */
    String getAccessToken();

    /**
     * 获取token是否有效
     */
    TokenValidDto getTokenValid(HttpServletRequest request);

    /**
     * 用户授权手机号记录接口
     */
    void authRecord();

    /**
     * 我的碳账本数据接口
     */
    MyCarbonBookDataVo myCarbonBook();

    SaveNewsConfigDto queryReadConfig();

    Integer sendUserReadPoints(SendUserReadPointsDto sendUserReadPointsDto);

    Integer sendUserReadPoints2(SendUserReadPointsDto sendUserReadPointsDto);

    void processReadPoints();
}