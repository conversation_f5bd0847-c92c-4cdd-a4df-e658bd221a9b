spring:
  shardingsphere:
    datasource:
      names: ds0
      ds0:
        driver-class-name: com.mysql.cj.jdbc.Driver
        type: com.zaxxer.hikari.HikariDataSource
        jdbc-url: **************************************************************************************************************************************
        username: hefei_applet
        password: DEVp7e5u6vs!@#com
        hikari:
          auto-commit: true
          connection-test-query: SELECT 1
          connection-timeout: 60000
          idle-timeout: 60000
          max-lifetime: 900000
          maximum-pool-size: 50
          pool-name: HikariCP
    sharding:
      tables:
        gl_points:
          actual-data-nodes: ds0.gl_points_00,ds0.gl_points_01,ds0.gl_points_02,ds0.gl_points_03,ds0.gl_points_04,ds0.gl_points_05,ds0.gl_points_06,ds0.gl_points_07,ds0.gl_points_08,ds0.gl_points_09,ds0.gl_points_0a,ds0.gl_points_0b,ds0.gl_points_0c,ds0.gl_points_0d,ds0.gl_points_0e,ds0.gl_points_0f,ds0.gl_points_10,ds0.gl_points_11,ds0.gl_points_12,ds0.gl_points_13,ds0.gl_points_14,ds0.gl_points_15,ds0.gl_points_16,ds0.gl_points_17,ds0.gl_points_18,ds0.gl_points_19,ds0.gl_points_1a,ds0.gl_points_1b,ds0.gl_points_1c,ds0.gl_points_1d,ds0.gl_points_1e,ds0.gl_points_1f,ds0.gl_points_20,ds0.gl_points_21,ds0.gl_points_22,ds0.gl_points_23,ds0.gl_points_24,ds0.gl_points_25,ds0.gl_points_26,ds0.gl_points_27,ds0.gl_points_28,ds0.gl_points_29,ds0.gl_points_2a,ds0.gl_points_2b,ds0.gl_points_2c,ds0.gl_points_2d,ds0.gl_points_2e,ds0.gl_points_2f,ds0.gl_points_30,ds0.gl_points_31,ds0.gl_points_32,ds0.gl_points_33,ds0.gl_points_34,ds0.gl_points_35,ds0.gl_points_36,ds0.gl_points_37,ds0.gl_points_38,ds0.gl_points_39,ds0.gl_points_3a,ds0.gl_points_3b,ds0.gl_points_3c,ds0.gl_points_3d,ds0.gl_points_3e,ds0.gl_points_3f,ds0.gl_points_40,ds0.gl_points_41,ds0.gl_points_42,ds0.gl_points_43,ds0.gl_points_44,ds0.gl_points_45,ds0.gl_points_46,ds0.gl_points_47,ds0.gl_points_48,ds0.gl_points_49,ds0.gl_points_4a,ds0.gl_points_4b,ds0.gl_points_4c,ds0.gl_points_4d,ds0.gl_points_4e,ds0.gl_points_4f,ds0.gl_points_50,ds0.gl_points_51,ds0.gl_points_52,ds0.gl_points_53,ds0.gl_points_54,ds0.gl_points_55,ds0.gl_points_56,ds0.gl_points_57,ds0.gl_points_58,ds0.gl_points_59,ds0.gl_points_5a,ds0.gl_points_5b,ds0.gl_points_5c,ds0.gl_points_5d,ds0.gl_points_5e,ds0.gl_points_5f,ds0.gl_points_60,ds0.gl_points_61,ds0.gl_points_62,ds0.gl_points_63,ds0.gl_points_64,ds0.gl_points_65,ds0.gl_points_66,ds0.gl_points_67,ds0.gl_points_68,ds0.gl_points_69,ds0.gl_points_6a,ds0.gl_points_6b,ds0.gl_points_6c,ds0.gl_points_6d,ds0.gl_points_6e,ds0.gl_points_6f,ds0.gl_points_70,ds0.gl_points_71,ds0.gl_points_72,ds0.gl_points_73,ds0.gl_points_74,ds0.gl_points_75,ds0.gl_points_76,ds0.gl_points_77,ds0.gl_points_78,ds0.gl_points_79,ds0.gl_points_7a,ds0.gl_points_7b,ds0.gl_points_7c,ds0.gl_points_7d,ds0.gl_points_7e,ds0.gl_points_7f,ds0.gl_points_80,ds0.gl_points_81,ds0.gl_points_82,ds0.gl_points_83,ds0.gl_points_84,ds0.gl_points_85,ds0.gl_points_86,ds0.gl_points_87,ds0.gl_points_88,ds0.gl_points_89,ds0.gl_points_8a,ds0.gl_points_8b,ds0.gl_points_8c,ds0.gl_points_8d,ds0.gl_points_8e,ds0.gl_points_8f,ds0.gl_points_90,ds0.gl_points_91,ds0.gl_points_92,ds0.gl_points_93,ds0.gl_points_94,ds0.gl_points_95,ds0.gl_points_96,ds0.gl_points_97,ds0.gl_points_98,ds0.gl_points_99,ds0.gl_points_9a,ds0.gl_points_9b,ds0.gl_points_9c,ds0.gl_points_9d,ds0.gl_points_9e,ds0.gl_points_9f,ds0.gl_points_a0,ds0.gl_points_a1,ds0.gl_points_a2,ds0.gl_points_a3,ds0.gl_points_a4,ds0.gl_points_a5,ds0.gl_points_a6,ds0.gl_points_a7,ds0.gl_points_a8,ds0.gl_points_a9,ds0.gl_points_aa,ds0.gl_points_ab,ds0.gl_points_ac,ds0.gl_points_ad,ds0.gl_points_ae,ds0.gl_points_af,ds0.gl_points_b0,ds0.gl_points_b1,ds0.gl_points_b2,ds0.gl_points_b3,ds0.gl_points_b4,ds0.gl_points_b5,ds0.gl_points_b6,ds0.gl_points_b7,ds0.gl_points_b8,ds0.gl_points_b9,ds0.gl_points_ba,ds0.gl_points_bb,ds0.gl_points_bc,ds0.gl_points_bd,ds0.gl_points_be,ds0.gl_points_bf,ds0.gl_points_c0,ds0.gl_points_c1,ds0.gl_points_c2,ds0.gl_points_c3,ds0.gl_points_c4,ds0.gl_points_c5,ds0.gl_points_c6,ds0.gl_points_c7,ds0.gl_points_c8,ds0.gl_points_c9,ds0.gl_points_ca,ds0.gl_points_cb,ds0.gl_points_cc,ds0.gl_points_cd,ds0.gl_points_ce,ds0.gl_points_cf,ds0.gl_points_d0,ds0.gl_points_d1,ds0.gl_points_d2,ds0.gl_points_d3,ds0.gl_points_d4,ds0.gl_points_d5,ds0.gl_points_d6,ds0.gl_points_d7,ds0.gl_points_d8,ds0.gl_points_d9,ds0.gl_points_da,ds0.gl_points_db,ds0.gl_points_dc,ds0.gl_points_dd,ds0.gl_points_de,ds0.gl_points_df,ds0.gl_points_e0,ds0.gl_points_e1,ds0.gl_points_e2,ds0.gl_points_e3,ds0.gl_points_e4,ds0.gl_points_e5,ds0.gl_points_e6,ds0.gl_points_e7,ds0.gl_points_e8,ds0.gl_points_e9,ds0.gl_points_ea,ds0.gl_points_eb,ds0.gl_points_ec,ds0.gl_points_ed,ds0.gl_points_ee,ds0.gl_points_ef,ds0.gl_points_f0,ds0.gl_points_f1,ds0.gl_points_f2,ds0.gl_points_f3,ds0.gl_points_f4,ds0.gl_points_f5,ds0.gl_points_f6,ds0.gl_points_f7,ds0.gl_points_f8,ds0.gl_points_f9,ds0.gl_points_fa,ds0.gl_points_fb,ds0.gl_points_fc,ds0.gl_points_fd,ds0.gl_points_fe,ds0.gl_points_ff
          table-strategy:
            standard:
              precise-algorithm-class-name: com.lvpuhui.gic.wxapp.infrastructure.config.CustomShardingTableAlgorithm
              sharding-column: mobile_sha256
        gl_behavior:
          actual-data-nodes: ds0.gl_behavior_00,ds0.gl_behavior_01,ds0.gl_behavior_02,ds0.gl_behavior_03,ds0.gl_behavior_04,ds0.gl_behavior_05,ds0.gl_behavior_06,ds0.gl_behavior_07,ds0.gl_behavior_08,ds0.gl_behavior_09,ds0.gl_behavior_0a,ds0.gl_behavior_0b,ds0.gl_behavior_0c,ds0.gl_behavior_0d,ds0.gl_behavior_0e,ds0.gl_behavior_0f,ds0.gl_behavior_10,ds0.gl_behavior_11,ds0.gl_behavior_12,ds0.gl_behavior_13,ds0.gl_behavior_14,ds0.gl_behavior_15,ds0.gl_behavior_16,ds0.gl_behavior_17,ds0.gl_behavior_18,ds0.gl_behavior_19,ds0.gl_behavior_1a,ds0.gl_behavior_1b,ds0.gl_behavior_1c,ds0.gl_behavior_1d,ds0.gl_behavior_1e,ds0.gl_behavior_1f,ds0.gl_behavior_20,ds0.gl_behavior_21,ds0.gl_behavior_22,ds0.gl_behavior_23,ds0.gl_behavior_24,ds0.gl_behavior_25,ds0.gl_behavior_26,ds0.gl_behavior_27,ds0.gl_behavior_28,ds0.gl_behavior_29,ds0.gl_behavior_2a,ds0.gl_behavior_2b,ds0.gl_behavior_2c,ds0.gl_behavior_2d,ds0.gl_behavior_2e,ds0.gl_behavior_2f,ds0.gl_behavior_30,ds0.gl_behavior_31,ds0.gl_behavior_32,ds0.gl_behavior_33,ds0.gl_behavior_34,ds0.gl_behavior_35,ds0.gl_behavior_36,ds0.gl_behavior_37,ds0.gl_behavior_38,ds0.gl_behavior_39,ds0.gl_behavior_3a,ds0.gl_behavior_3b,ds0.gl_behavior_3c,ds0.gl_behavior_3d,ds0.gl_behavior_3e,ds0.gl_behavior_3f,ds0.gl_behavior_40,ds0.gl_behavior_41,ds0.gl_behavior_42,ds0.gl_behavior_43,ds0.gl_behavior_44,ds0.gl_behavior_45,ds0.gl_behavior_46,ds0.gl_behavior_47,ds0.gl_behavior_48,ds0.gl_behavior_49,ds0.gl_behavior_4a,ds0.gl_behavior_4b,ds0.gl_behavior_4c,ds0.gl_behavior_4d,ds0.gl_behavior_4e,ds0.gl_behavior_4f,ds0.gl_behavior_50,ds0.gl_behavior_51,ds0.gl_behavior_52,ds0.gl_behavior_53,ds0.gl_behavior_54,ds0.gl_behavior_55,ds0.gl_behavior_56,ds0.gl_behavior_57,ds0.gl_behavior_58,ds0.gl_behavior_59,ds0.gl_behavior_5a,ds0.gl_behavior_5b,ds0.gl_behavior_5c,ds0.gl_behavior_5d,ds0.gl_behavior_5e,ds0.gl_behavior_5f,ds0.gl_behavior_60,ds0.gl_behavior_61,ds0.gl_behavior_62,ds0.gl_behavior_63,ds0.gl_behavior_64,ds0.gl_behavior_65,ds0.gl_behavior_66,ds0.gl_behavior_67,ds0.gl_behavior_68,ds0.gl_behavior_69,ds0.gl_behavior_6a,ds0.gl_behavior_6b,ds0.gl_behavior_6c,ds0.gl_behavior_6d,ds0.gl_behavior_6e,ds0.gl_behavior_6f,ds0.gl_behavior_70,ds0.gl_behavior_71,ds0.gl_behavior_72,ds0.gl_behavior_73,ds0.gl_behavior_74,ds0.gl_behavior_75,ds0.gl_behavior_76,ds0.gl_behavior_77,ds0.gl_behavior_78,ds0.gl_behavior_79,ds0.gl_behavior_7a,ds0.gl_behavior_7b,ds0.gl_behavior_7c,ds0.gl_behavior_7d,ds0.gl_behavior_7e,ds0.gl_behavior_7f,ds0.gl_behavior_80,ds0.gl_behavior_81,ds0.gl_behavior_82,ds0.gl_behavior_83,ds0.gl_behavior_84,ds0.gl_behavior_85,ds0.gl_behavior_86,ds0.gl_behavior_87,ds0.gl_behavior_88,ds0.gl_behavior_89,ds0.gl_behavior_8a,ds0.gl_behavior_8b,ds0.gl_behavior_8c,ds0.gl_behavior_8d,ds0.gl_behavior_8e,ds0.gl_behavior_8f,ds0.gl_behavior_90,ds0.gl_behavior_91,ds0.gl_behavior_92,ds0.gl_behavior_93,ds0.gl_behavior_94,ds0.gl_behavior_95,ds0.gl_behavior_96,ds0.gl_behavior_97,ds0.gl_behavior_98,ds0.gl_behavior_99,ds0.gl_behavior_9a,ds0.gl_behavior_9b,ds0.gl_behavior_9c,ds0.gl_behavior_9d,ds0.gl_behavior_9e,ds0.gl_behavior_9f,ds0.gl_behavior_a0,ds0.gl_behavior_a1,ds0.gl_behavior_a2,ds0.gl_behavior_a3,ds0.gl_behavior_a4,ds0.gl_behavior_a5,ds0.gl_behavior_a6,ds0.gl_behavior_a7,ds0.gl_behavior_a8,ds0.gl_behavior_a9,ds0.gl_behavior_aa,ds0.gl_behavior_ab,ds0.gl_behavior_ac,ds0.gl_behavior_ad,ds0.gl_behavior_ae,ds0.gl_behavior_af,ds0.gl_behavior_b0,ds0.gl_behavior_b1,ds0.gl_behavior_b2,ds0.gl_behavior_b3,ds0.gl_behavior_b4,ds0.gl_behavior_b5,ds0.gl_behavior_b6,ds0.gl_behavior_b7,ds0.gl_behavior_b8,ds0.gl_behavior_b9,ds0.gl_behavior_ba,ds0.gl_behavior_bb,ds0.gl_behavior_bc,ds0.gl_behavior_bd,ds0.gl_behavior_be,ds0.gl_behavior_bf,ds0.gl_behavior_c0,ds0.gl_behavior_c1,ds0.gl_behavior_c2,ds0.gl_behavior_c3,ds0.gl_behavior_c4,ds0.gl_behavior_c5,ds0.gl_behavior_c6,ds0.gl_behavior_c7,ds0.gl_behavior_c8,ds0.gl_behavior_c9,ds0.gl_behavior_ca,ds0.gl_behavior_cb,ds0.gl_behavior_cc,ds0.gl_behavior_cd,ds0.gl_behavior_ce,ds0.gl_behavior_cf,ds0.gl_behavior_d0,ds0.gl_behavior_d1,ds0.gl_behavior_d2,ds0.gl_behavior_d3,ds0.gl_behavior_d4,ds0.gl_behavior_d5,ds0.gl_behavior_d6,ds0.gl_behavior_d7,ds0.gl_behavior_d8,ds0.gl_behavior_d9,ds0.gl_behavior_da,ds0.gl_behavior_db,ds0.gl_behavior_dc,ds0.gl_behavior_dd,ds0.gl_behavior_de,ds0.gl_behavior_df,ds0.gl_behavior_e0,ds0.gl_behavior_e1,ds0.gl_behavior_e2,ds0.gl_behavior_e3,ds0.gl_behavior_e4,ds0.gl_behavior_e5,ds0.gl_behavior_e6,ds0.gl_behavior_e7,ds0.gl_behavior_e8,ds0.gl_behavior_e9,ds0.gl_behavior_ea,ds0.gl_behavior_eb,ds0.gl_behavior_ec,ds0.gl_behavior_ed,ds0.gl_behavior_ee,ds0.gl_behavior_ef,ds0.gl_behavior_f0,ds0.gl_behavior_f1,ds0.gl_behavior_f2,ds0.gl_behavior_f3,ds0.gl_behavior_f4,ds0.gl_behavior_f5,ds0.gl_behavior_f6,ds0.gl_behavior_f7,ds0.gl_behavior_f8,ds0.gl_behavior_f9,ds0.gl_behavior_fa,ds0.gl_behavior_fb,ds0.gl_behavior_fc,ds0.gl_behavior_fd,ds0.gl_behavior_fe,ds0.gl_behavior_ff
          table-strategy:
            standard:
              precise-algorithm-class-name: com.lvpuhui.gic.wxapp.infrastructure.config.CustomShardingTableAlgorithm
              sharding-column: mobile_sha256
    props:
      sql:
        show: false
      max:
        connections:
          size:
            per:
              query: 1
# 后台管理api地址
webconsole_api: https://eapitest.lvpuhui.com/hefei/webapi/
scene-category:
  green-life: 25
  green-capital: 26

oss:
  accessKeyId: LTAI5t6hF5j8woNQHTdWhi61
  accessKeySecret: ******************************
  endpoint: oss-cn-beijing.aliyuncs.com
  bucketName: lvpuhui-public
file:
  path: demo/hefei/

application:
  aeskey: 18febf6957b2946adc78ddfc93f3dfbe
  aesiv: 1a9e0f925d4eb002