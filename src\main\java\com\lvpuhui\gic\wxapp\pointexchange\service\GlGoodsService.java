package com.lvpuhui.gic.wxapp.pointexchange.service;

import com.lvpuhui.gic.wxapp.homepage.dto.AccumulateObtainGoods;
import com.lvpuhui.gic.wxapp.homepage.dto.AccumulateObtainGoodsDto;
import com.lvpuhui.gic.wxapp.homepage.dto.YesterdayObtainGoods;
import com.lvpuhui.gic.wxapp.homepage.dto.YesterdayObtainGoodsDto;
import com.lvpuhui.gic.wxapp.pointexchange.dto.*;
import com.lvpuhui.gic.wxapp.pointexchange.entity.GlGoods;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 商品表(GlGoods)表服务接口
 * <AUTHOR>
 * @since 2022年5月5日 19:23:17
 */
public interface GlGoodsService extends IService<GlGoods> {

    /**
     * 商品列表接口
     */
    List<Goods> goods();

    /**
     * 商品详情接口
     */
    GoodsDetail goodsDetail(GoodsDetailDto goodsDetailDto);

    /**
     * 兑换商品接口
     */
    GoodsExchange goodsExchange(GoodsExchangeDto goodsExchangeDto);

    /**
     * 已兑换商品列表
     */
    List<GoodsUsed> goodsUsed(GoodsUsedDto goodsUsedDto);

    /**
     * 已兑换商品详情接口
     */
    GoodsExchangedDetail goodsDetailUsed(GoodsExchangedDetailDto goodsExchangedDetailDto);

    /**
     * 昨日排行领取商品接口
     */
    YesterdayObtainGoods yesterdayObtain(YesterdayObtainGoodsDto rankAccumulateListDto);

    /**
     * 累积排行领取商品接口
     */
    AccumulateObtainGoods accumulateObtain(AccumulateObtainGoodsDto accumulateObtainGoodsDto);

    void proGoods();
}
