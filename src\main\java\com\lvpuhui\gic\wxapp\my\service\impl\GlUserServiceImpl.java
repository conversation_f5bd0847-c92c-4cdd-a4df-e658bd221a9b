package com.lvpuhui.gic.wxapp.my.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.lvpuhui.gic.wxapp.base.dao.GlAppletConfigDao;
import com.lvpuhui.gic.wxapp.base.dao.GlAuthTokenDao;
import com.lvpuhui.gic.wxapp.base.dao.GlTokenDao;
import com.lvpuhui.gic.wxapp.base.entity.GlAppletConfig;
import com.lvpuhui.gic.wxapp.base.entity.GlAuthTokenDO;
import com.lvpuhui.gic.wxapp.base.entity.GlToken;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.carbon.enums.EAppletConfig;
import com.lvpuhui.gic.wxapp.carbon.enums.UserPointsSettingEnum;
import com.lvpuhui.gic.wxapp.carbonbook.dto.CarbonBooksVo;
import com.lvpuhui.gic.wxapp.carbonbook.service.CarbonBookService;
import com.lvpuhui.gic.wxapp.homepage.dao.GlRankDao;
import com.lvpuhui.gic.wxapp.homepage.dto.MyCarbonBookDataVo;
import com.lvpuhui.gic.wxapp.homepage.dto.RankConfigDto;
import com.lvpuhui.gic.wxapp.homepage.entity.GlRank;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.infrastructure.utils.CalcUtil;
import com.lvpuhui.gic.wxapp.infrastructure.utils.UserUtils;
import com.lvpuhui.gic.wxapp.my.dao.GlBusinessPointsLogDao;
import com.lvpuhui.gic.wxapp.my.dao.GlChangeBindDao;
import com.lvpuhui.gic.wxapp.my.dao.GlUserAuthRecordDao;
import com.lvpuhui.gic.wxapp.my.dao.GlUserDao;
import com.lvpuhui.gic.wxapp.my.dto.*;
import com.lvpuhui.gic.wxapp.my.entity.GlBusinessPointsLogDO;
import com.lvpuhui.gic.wxapp.my.entity.GlChangeBind;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.my.entity.GlUserAuthRecord;
import com.lvpuhui.gic.wxapp.my.service.GlUserFriendsService;
import com.lvpuhui.gic.wxapp.my.service.GlUserService;
import com.lvpuhui.gic.wxapp.other.utils.AES256Util;
import com.lvpuhui.gic.wxapp.pointexchange.dto.PointData;
import com.lvpuhui.gic.wxapp.pointexchange.dto.PointDataDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 用户积分表(GlUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 15:16:32
 */
@Slf4j
@Service("glUserService")
public class GlUserServiceImpl extends ServiceImpl<GlUserDao, GlUser> implements GlUserService {

    @Resource
    private GlTokenDao glTokenDao;

    @Autowired
    private GlPointsService glPointsService;
    @Resource
    GlUserFriendsService glUserFriendsService;

    @Resource
    private GlChangeBindDao glChangeBindDao;

    @Resource
    private GlAuthTokenDao glAuthTokenDao;

    @Resource
    private GlUserAuthRecordDao glUserAuthRecordDao;

    @Resource
    private AES256Util aes256Util;

    @Resource
    private GlRankDao glRankDao;

    @Value("${spring.profiles.active}")
    private String profiles;

    @Resource
    private GlAppletConfigDao glAppletConfigDao;

    @Resource
    private GlBusinessPointsLogDao glBusinessPointsLogDao;

    @Resource
    GlAppletConfigService glAppletConfigService;

    @Resource
    private CarbonBookService carbonBookService;

    /**
     * 重试次数
     */
    private Integer retryNum = 0;

    @Override
    public PointData pointData(PointDataDto pointDataDto) {
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getMobileSha256,pointDataDto.getMobileSha256());
        glUserQuery.last("limit 1");
        GlUser glUser = baseMapper.selectOne(glUserQuery);
        if(Objects.isNull(glUser)){
            throw new GicWxAppException("请您先授权");
        }
        PointData pointData = new PointData();
        BeanUtil.copyProperties(glUser,pointData);
        return pointData;
    }

    @Override
    public UserInfo userAuth(UserAuthDto userAuthDto) {
        String mobileSha256 = UserUtils.getMobileSha256();
        GlUser glUser = baseMapper.selectById(UserUtils.getUserId());

        LambdaUpdateWrapper<GlUser> glUserLambdaUpdateWrapper = Wrappers.lambdaUpdate();
        glUserLambdaUpdateWrapper.eq(GlUser::getId,glUser.getId());

        GlUser updateGlUser = new GlUser();
        if(StrUtil.isNotBlank(userAuthDto.getAvatarUrl())){
            updateGlUser.setAvatarUrl(userAuthDto.getAvatarUrl());
        }
        if(StrUtil.isNotBlank(userAuthDto.getNickName())){
            updateGlUser.setNickName(userAuthDto.getNickName());
        }
        updateGlUser.setUpdated(new Date());
        baseMapper.update(updateGlUser,glUserLambdaUpdateWrapper);

        // 邀请人发放积分
        if(StrUtil.isNotBlank(userAuthDto.getSharedSha256())){
            try {
                if(userAuthDto.getSharedSha256().equals(mobileSha256)){
                    log.error("邀请人是本人 {}",mobileSha256);
                }else {
                    glUserFriendsService.grantPoints(mobileSha256,
                            userAuthDto.getSharedSha256(),userAuthDto.getNickName());
                }
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }
        }
        String cryptoMobile = glUser.getMobile();
        String mobile = "";
        if(Validator.isMobile(cryptoMobile)){
            mobile = cryptoMobile;
        }else {
            mobile = aes256Util.decrypt(cryptoMobile);
        }
        UserInfo userInfo = new UserInfo();
        userInfo.setId(glUser.getId());
        userInfo.setMobileSha256(glUser.getMobileSha256());
        userInfo.setAvatarUrl(userAuthDto.getAvatarUrl());
        userInfo.setNickName(userAuthDto.getNickName());
        userInfo.setMobile(String.valueOf(PhoneUtil.hideBetween(mobile)));
        return userInfo;
    }

    @Override
    public CodeOpenid code2session(CodeOpenidDto codeOpenidDto) {
        /*
         * 1.拿到openid
         * 2.拿到access_token
         * 3.拿到手机号
         */
        String openid = getOpenid(codeOpenidDto.getJsCode());
        // 查询用户
        LambdaQueryWrapper<GlUser> glUserQuery = Wrappers.lambdaQuery();
        glUserQuery.eq(GlUser::getOpenId,openid);
        glUserQuery.last("limit 1");
        GlUser glUser = baseMapper.selectOne(glUserQuery);
        // 获取手机号
        String mobile = getPhoneNumber(codeOpenidDto.getMobileCode());
        String cryptoMobile = "";
        try {
            cryptoMobile = aes256Util.encrypt(mobile);
        }catch (Exception e){
            cryptoMobile = mobile;
            log.error("cryptoMobile error",e);
        }
        String mobileSha256 = SecureUtil.sha256(mobile);
        if(Objects.isNull(glUser)){
            // 查询用户
            LambdaQueryWrapper<GlUser> glUserByMobileQuery = Wrappers.lambdaQuery();
            glUserByMobileQuery.eq(GlUser::getMobileSha256,mobileSha256);
            glUserByMobileQuery.last("limit 1");
            GlUser mobileGlUser = baseMapper.selectOne(glUserByMobileQuery);
            if(Objects.isNull(mobileGlUser)){
                glUser = new GlUser();
                glUser.setMobile(cryptoMobile);
                glUser.setMobileSha256(mobileSha256);
                glUser.setOpenId(openid);
                glUser.setCreated(new Date());
                glUser.setUpdated(new Date());

                baseMapper.insert(glUser);
            }else {
                glUser = mobileGlUser;
                glUser.setMobile(cryptoMobile);
                glUser.setMobileSha256(mobileSha256);
                glUser.setOpenId(openid);
                glUser.setUpdated(new Date());
                baseMapper.updateById(glUser);
            }
        }else {
            // 查询用户
            LambdaQueryWrapper<GlUser> glUserByMobileQuery = Wrappers.lambdaQuery();
            glUserByMobileQuery.eq(GlUser::getMobileSha256,mobileSha256);
            glUserByMobileQuery.last("limit 1");
            GlUser mobileGlUser = baseMapper.selectOne(glUserByMobileQuery);
            if(Objects.nonNull(mobileGlUser)){
                mobileGlUser.setMobile(cryptoMobile);
                mobileGlUser.setMobileSha256(mobileSha256);
                mobileGlUser.setOpenId(openid);
                mobileGlUser.setUpdated(new Date());
                mobileGlUser.setAvatarUrl(glUser.getAvatarUrl());
                mobileGlUser.setNickName(glUser.getNickName());
                baseMapper.updateById(mobileGlUser);
            }else {
                // 记录一下换绑手机号
                try {
                    GlChangeBind glChangeBind = new GlChangeBind();
                    glChangeBind.setOriginalMobile(glUser.getMobile());
                    glChangeBind.setOriginalMobileSha256(glUser.getMobileSha256());
                    glChangeBind.setMobile(cryptoMobile);
                    glChangeBind.setMobileSha256(mobileSha256);
                    glChangeBind.setUserId(glUser.getId());
                    glChangeBindDao.insert(glChangeBind);
                }catch (Exception e){
                    log.error("新增换绑记录异常:{}",e);
                }
                glUser.setMobile(cryptoMobile);
                glUser.setMobileSha256(mobileSha256);
                glUser.setOpenId(openid);
                glUser.setUpdated(new Date());
                baseMapper.updateById(glUser);
            }
        }
        // 用户授权的时候 查询下积分表 算出来 总积分/剩余积分/使用积分
        glPointsService.calcUserPoints(glUser.getMobileSha256());

        LambdaQueryWrapper<GlAuthTokenDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(GlAuthTokenDO::getUserId,glUser.getId());
        GlAuthTokenDO glAuthTokenDO = glAuthTokenDao.selectOne(lambdaQueryWrapper);
        if(Objects.nonNull(glAuthTokenDO)){
            glAuthTokenDO.setToken(IdUtil.fastSimpleUUID());
            glAuthTokenDao.updateById(glAuthTokenDO);
        }else {
            glAuthTokenDO = new GlAuthTokenDO();
            glAuthTokenDO.setToken(IdUtil.fastSimpleUUID());
            glAuthTokenDO.setUserId(glUser.getId());
            glAuthTokenDao.insert(glAuthTokenDO);
        }

        GlRank glRank = glRankDao.selectById(mobileSha256);
        if(Objects.isNull(glRank)){
            glRank = new GlRank();
            glRank.setMobileSha256(mobileSha256);
            glRank.setRank(-1L);
            CarbonBooksVo carbonBooksByMobileSha256 = carbonBookService.getCarbonBooksByMobileSha256(mobileSha256);
            if(Objects.nonNull(carbonBooksByMobileSha256)){
                glRank.setEmission(Objects.isNull(carbonBooksByMobileSha256.getEmissionNum())?0:carbonBooksByMobileSha256.getEmissionNum());
            }
            glRankDao.insert(glRank);
        }

        CodeOpenid codeOpenid = new CodeOpenid();
        codeOpenid.setAvatarUrl(glUser.getAvatarUrl());
        codeOpenid.setNickName(glUser.getNickName());
        codeOpenid.setOpenid(openid);
        codeOpenid.setMobileSha256(mobileSha256);
        codeOpenid.setToken(glAuthTokenDO.getToken());
        return codeOpenid;
    }

    public String getOpenid(String jsCode){
        String appId = Global.APP_ID;
        String secret = Global.SECRET;
        String code2SessionUrl = Global.WX_CODE2SESSION;
        code2SessionUrl = code2SessionUrl
                .replaceAll("APPID",appId)
                .replaceAll("SECRET",secret)
                .replaceAll("JSCODE",jsCode);
        String code2SessionResult = HttpUtil.get(code2SessionUrl);
        log.info("获取openid,地址:{},结果:{}",code2SessionUrl,code2SessionResult);
        JSONObject jsonObject = JSON.parseObject(code2SessionResult);
        if(jsonObject.containsKey("openid")){
            return jsonObject.getString("openid");
        }
        Integer errcode = jsonObject.getInteger("errcode");
        if(errcode == 40029){
            log.info("code失效");
            throw new GicWxAppException("请重新进入小程序");
        }
        if(errcode == 40163){
            log.info("code已失效");
            throw new GicWxAppException("请重新进入小程序");
        }
        throw new GicWxAppException("授权失败,请您重试");
    }

    /**
     * 获取手机号
     */
    public String getPhoneNumber(String mobileCode){
        // 获取手机号
        String accessToken = getAccessToken();
        String userPhoneUrl = Global.WX_USER_PHONE;
        userPhoneUrl = userPhoneUrl
                .replaceAll("ACCESS_TOKEN",accessToken);

        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("code",mobileCode);
        String userPhoneResult = HttpUtil.post(userPhoneUrl, JSON.toJSONString(paramMap));
        log.info("获取手机号,地址:{},结果:{}",userPhoneUrl,userPhoneResult);
        JSONObject jsonObject = JSON.parseObject(userPhoneResult);
        Integer errcode = jsonObject.getInteger("errcode");
        if(errcode == 0){
            retryNum = 0;
            return jsonObject.getJSONObject("phone_info").getString("purePhoneNumber");
        }
        if(errcode == 40029){
            throw new GicWxAppException("【不合法的code】请重试");
        }
        if(retryNum >= 3){
            throw new GicWxAppException("授权失败,请重试或联系管理员");
        }
        if(errcode == 40001){
            glTokenDao.delete(null);
            retryNum++;
            return getPhoneNumber(mobileCode);
        }
        throw new GicWxAppException("未知错误,请联系管理员");
    }

    /**
     * 获取access_token
     */
    @Override
    public String getAccessToken(){
        String appId = Global.APP_ID;
        String secret = Global.SECRET;
        LambdaQueryWrapper<GlToken> glTokenQuery = Wrappers.lambdaQuery();
        glTokenQuery.orderByDesc(GlToken::getExpired);
        glTokenQuery.last("limit 1");
        GlToken glToken = glTokenDao.selectOne(glTokenQuery);
        if(Objects.nonNull(glToken)){
            Date expired = glToken.getExpired();
            if(DateUtil.compare(new Date(),expired) < 0){
                return glToken.getToken();
            }
        }
        // 从微信获取
        String accessTokenUrl = Global.WX_ACCESS_TOKEN;
        accessTokenUrl = accessTokenUrl
                .replaceAll("APPID",appId)
                .replaceAll("APPSECRET",secret);
        String accessTokenResult = HttpUtil.get(accessTokenUrl);
        log.info("获取token,地址:{},结果:{}",accessTokenUrl,accessTokenResult);
        String accessToken = JSON.parseObject(accessTokenResult).getString("access_token");
        if(Objects.isNull(glToken)){
            glToken = new GlToken();
            glToken.setToken(accessToken);
            glToken.setExpired(DateUtil.offset(new Date(), DateField.MINUTE,100));
            glTokenDao.insert(glToken);
            return accessToken;
        }
        glToken.setToken(accessToken);
        glToken.setExpired(DateUtil.offset(new Date(), DateField.MINUTE,100));
        glTokenDao.updateById(glToken);
        return accessToken;
    }

    @Override
    public TokenValidDto getTokenValid(HttpServletRequest request) {
        TokenValidDto tokenValidDto = new TokenValidDto();
        tokenValidDto.setValid(0);
        String token = request.getHeader("token");
        if(StrUtil.isBlank(token)){
            return tokenValidDto;
        }
        LambdaQueryWrapper<GlAuthTokenDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(GlAuthTokenDO::getToken,token);
        GlAuthTokenDO glAuthTokenDO = glAuthTokenDao.selectOne(lambdaQueryWrapper);
        if(Objects.isNull(glAuthTokenDO)){
            return tokenValidDto;
        }
        if(Objects.nonNull(glAuthTokenDO.getExpired()) && LocalDateTime.now().isAfter(glAuthTokenDO.getExpired())){
            return tokenValidDto;
        }
        GlUser glUser = baseMapper.selectById(glAuthTokenDO.getUserId());
        if(Objects.isNull(glUser)){
            return tokenValidDto;
        }
        tokenValidDto.setValid(1);
        return tokenValidDto;
    }

    @Override
    public void authRecord() {
        GlUserAuthRecord glUserAuthRecord = new GlUserAuthRecord();
        glUserAuthRecord.setUserId(UserUtils.getUserId());
        glUserAuthRecord.setMobile(UserUtils.getUser().getMobile());
        glUserAuthRecord.setCreated(new Date());
        glUserAuthRecordDao.insert(glUserAuthRecord);
    }

    @Override
    public MyCarbonBookDataVo myCarbonBook() {
        Long userId = UserUtils.getUserId();
        String mobileSha256 = UserUtils.getMobileSha256();
        GlUser glUser = baseMapper.selectById(userId);
        MyCarbonBookDataVo myCarbonBookDataVo = new MyCarbonBookDataVo();
        myCarbonBookDataVo.setPointRemain(glUser.getPointRemain().longValue());

        RankConfigDto rankConfig = glAppletConfigService.getRankConfig();
        Integer top = rankConfig.getCumulative().getTop();
        GlRank glRank = glRankDao.selectById(glUser.getMobileSha256());
        LambdaQueryWrapper<GlRank> glRankLambdaQueryWrapper = Wrappers.lambdaQuery();
        glRankLambdaQueryWrapper.orderByDesc(GlRank::getRank);
        glRankLambdaQueryWrapper.last("limit 1");
        GlRank glRankLast = glRankDao.selectOne(glRankLambdaQueryWrapper);
        if(Objects.isNull(glRank)){
            glRank = new GlRank();
            glRank.setMobileSha256(mobileSha256);
            glRank.setRank(-1L);
            CarbonBooksVo carbonBooksByMobileSha256 = carbonBookService.getCarbonBooksByMobileSha256(mobileSha256);
            if(Objects.nonNull(carbonBooksByMobileSha256)){
                glRank.setEmission(Objects.isNull(carbonBooksByMobileSha256.getEmissionNum())?0:carbonBooksByMobileSha256.getEmissionNum());
            }
            glRankDao.insert(glRank);
        }
        if(Objects.nonNull(glRank.getRank())){
            myCarbonBookDataVo.setEmissionText(CalcUtil.weightFormat(glRank.getEmission()));
            if(glRank.getRank() > top){
                myCarbonBookDataVo.setRank(glRank.getCityRank());
                myCarbonBookDataVo.setRadio(CalcUtil.calcRankRadio(glRank.getCityRank(),glRankLast.getCityRank()));
            }else {
                myCarbonBookDataVo.setRadio(CalcUtil.calcRankRadio(glRank.getRank(),glRankLast.getCityRank()));
            }
            if(Objects.isNull(glRank.getEmission()) || glRank.getEmission() <= 0){
                myCarbonBookDataVo.setRadio(0D);
            }
        }
        if(Objects.nonNull(glRank.getRank()) && glRank.getRank() == -1){
            myCarbonBookDataVo.setRank(null);
        }
        return myCarbonBookDataVo;
    }

    @Override
    public SaveNewsConfigDto queryReadConfig() {
        GlAppletConfig isExit = glAppletConfigDao.selectByKey(EAppletConfig.READ_SETTING.name());
        if(Objects.nonNull(isExit)){
            return JSON.parseObject(isExit.getParamValue(),SaveNewsConfigDto.class);
        }else {
            return null;
        }
    }

    @Override
    public Integer sendUserReadPoints(SendUserReadPointsDto sendUserReadPointsDto) {
        Integer point = 0;
        LocalDate today = LocalDate.now();
        Long userId = UserUtils.getUserId();
        String mobile = UserUtils.getMobileSha256();
        String key = "read_"+userId+"_"+sendUserReadPointsDto.getNewId();
        //获取阅读类型
        int readType = UserPointsSettingEnum.READ_USER.getCode();
        GlAppletConfig isExit = glAppletConfigDao.selectByKey(EAppletConfig.READ_SETTING.name());
        if(Objects.nonNull(isExit)){
            SaveNewsConfigDto saveNewsConfigDto =  JSON.parseObject(isExit.getParamValue(),SaveNewsConfigDto.class);
            if(Objects.nonNull(saveNewsConfigDto)){
                //上限分数
                Integer points = saveNewsConfigDto.getEveryDayMaxPoints();
                Integer sumPoints = glBusinessPointsLogDao.queryUserReadPoints(readType,today,userId);
                int currentPoints = sumPoints ==null? 0:sumPoints;
                if(currentPoints <= points){
                    GlBusinessPointsLogDO glBusinessPointsLogDO = new GlBusinessPointsLogDO();
                    glBusinessPointsLogDO.setUserId(userId);
                    glBusinessPointsLogDO.setCreated(LocalDate.now());
                    glBusinessPointsLogDO.setBusinessType(UserPointsSettingEnum.READ_USER.getCode());
                    glBusinessPointsLogDO.setBusinessKey(key);
                    glBusinessPointsLogDO.setPoints(saveNewsConfigDto.getEveryDayNews());
                    try {
                        glBusinessPointsLogDao.insert(glBusinessPointsLogDO);
                        point = sendUserReadPointsDto.getPoints();
                        //发放积分
                        glPointsService.sendUserReadPoints(glBusinessPointsLogDO,mobile,LocalDateTime.now());
                    }catch (Exception e){
                        log.error("sendUserReadPoints",e);
                    }
                }
            }
        }

        return point;
    }

    @Override
    public Integer sendUserReadPoints2(SendUserReadPointsDto sendUserReadPointsDto) {
        Integer point = 0;
        Long userId = UserUtils.getUserId();
        String mobile = UserUtils.getMobileSha256();
        String key = "read_" + userId + "_" + sendUserReadPointsDto.getNewId();

        GlAppletConfig isExit = glAppletConfigDao.selectByKey(EAppletConfig.READ_SETTING.name());
        SaveNewsConfigDto saveNewsConfigDto =  JSON.parseObject(isExit.getParamValue(),SaveNewsConfigDto.class);

        // 检查用户是否已经对该新闻进行过阅读
        GlBusinessPointsLogDO existingLog = glBusinessPointsLogDao.selectOne(new LambdaQueryWrapper<GlBusinessPointsLogDO>()
                .eq(GlBusinessPointsLogDO::getUserId, userId)
                .eq(GlBusinessPointsLogDO::getBusinessKey, key).last("limit 1"));

        if (existingLog == null) {
            // 如果没有记录，则发放积分
            GlBusinessPointsLogDO glBusinessPointsLogDO = new GlBusinessPointsLogDO();
            glBusinessPointsLogDO.setUserId(userId);
            glBusinessPointsLogDO.setCreated(LocalDate.now());
            glBusinessPointsLogDO.setBusinessType(UserPointsSettingEnum.READ_USER.getCode());
            glBusinessPointsLogDO.setBusinessKey(key);
            glBusinessPointsLogDO.setPoints(saveNewsConfigDto.getEveryDayNews());

            try {
                glBusinessPointsLogDao.insert(glBusinessPointsLogDO);
                point = saveNewsConfigDto.getEveryDayNews();
                // 发放积分
//                glPointsService.sendUserReadPoints(glBusinessPointsLogDO, mobile);
            } catch (Exception e) {
                log.error("sendUserReadPoints", e);
            }
        }

        return point;
    }

    @Override
    public Integer sendUserReadPointsWithParams(SendUserReadPointsWithParamsDto dto) {
        int hour = RandomUtil.randomInt(10, 22);
        int minute = RandomUtil.randomInt(0, 59);
        int second = RandomUtil.randomInt(0, 59);
        LocalDateTime time = LocalDateTime.of(dto.getTime().toLocalDate(), LocalTime.of(hour, minute, second));
        dto.setTime( time);

        Integer point = 0;
        LocalDate today = dto.getTime().toLocalDate();
        Long userId = dto.getUserId();
        String mobile = dto.getMobile();
        String key = "read_" + userId + "_" + dto.getNewId();

        //获取阅读类型
        int readType = UserPointsSettingEnum.READ_USER.getCode();
        GlAppletConfig isExit = glAppletConfigDao.selectByKey(EAppletConfig.READ_SETTING.name());
        if(Objects.nonNull(isExit)){
            SaveNewsConfigDto saveNewsConfigDto = JSON.parseObject(isExit.getParamValue(), SaveNewsConfigDto.class);
            if(Objects.nonNull(saveNewsConfigDto)){
                //上限分数
                Integer points = saveNewsConfigDto.getEveryDayMaxPoints();
                Integer sumPoints = glBusinessPointsLogDao.queryUserReadPoints(readType, today, userId);
                int currentPoints = sumPoints == null ? 0 : sumPoints;
                if(currentPoints <= points){
                    GlBusinessPointsLogDO glBusinessPointsLogDO = new GlBusinessPointsLogDO();
                    glBusinessPointsLogDO.setUserId(userId);
                    glBusinessPointsLogDO.setCreated(today);
                    glBusinessPointsLogDO.setBusinessType(UserPointsSettingEnum.READ_USER.getCode());
                    glBusinessPointsLogDO.setBusinessKey(key);
                    glBusinessPointsLogDO.setPoints(saveNewsConfigDto.getEveryDayNews());
                    try {
                        glBusinessPointsLogDao.insert(glBusinessPointsLogDO);
                        point = dto.getPoints() != null ? dto.getPoints() : saveNewsConfigDto.getEveryDayNews();

                        //发放积分 - 使用传入的日期和时间
                        glPointsService.sendUserReadPoints(glBusinessPointsLogDO, mobile, dto.getTime());
                    } catch (Exception e) {
                        log.error("sendUserReadPointsWithParams", e);
                    }
                }
            }
        }

        return point;
    }

    @Override
    public void processReadPoints() {
        List<String> strings = FileUtil.readUtf8Lines("D:/process.log");
        List<String> matched_result = FileUtil.readUtf8Lines("D:/unmatched_result.txt");
        // matched_result每行是token,mobileSha256，给我转换成一个map
        Map<String, String> mobileSha256Map = new HashMap<>();
        for (String s : matched_result) {
            String[] split = s.split(",");
            mobileSha256Map.put(split[0], split[1]);
        }
        Set<String> keySet = mobileSha256Map.keySet();
        List<String> logList = new ArrayList<>();
        GlAppletConfig isExit = glAppletConfigDao.selectByKey(EAppletConfig.READ_SETTING.name());
        SaveNewsConfigDto saveNewsConfigDto =  JSON.parseObject(isExit.getParamValue(),SaveNewsConfigDto.class);
        for (String string : strings) {
            String[] split = string.split("\\|");
            String time = split[0];
            String token = split[1];
            String json = split[2];
            if(StrUtil.isBlank(token)){
                continue;
            }
            if(!keySet.contains(token)){
                continue;
            }
            String mobileSha256 = mobileSha256Map.get(token);
//            LambdaQueryWrapper<GlAuthTokenDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
//            lambdaQueryWrapper.eq(GlAuthTokenDO::getToken,token);
//            GlAuthTokenDO glAuthTokenDO = glAuthTokenDao.selectOne(lambdaQueryWrapper);
//            if(Objects.isNull(glAuthTokenDO)){
//                logList.add(token);
//                continue;
//            }
//            Long userId = glAuthTokenDO.getUserId();
            LambdaQueryWrapper<GlUser> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.eq(GlUser::getMobileSha256,mobileSha256);
            GlUser glUser = baseMapper.selectOne(lambdaQueryWrapper);
            if(Objects.isNull(glUser)){
                continue;
            }
            LocalDateTime localDateTime = LocalDateTime.parse( time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
            SendUserReadPointsDto sendUserReadPointsDto = JSON.parseObject(json,SendUserReadPointsDto.class);
            LocalDate today = localDateTime.toLocalDate();
            String key = "read_"+glUser.getId()+"_"+sendUserReadPointsDto.getNewId();
            //获取阅读类型
            int readType = UserPointsSettingEnum.READ_USER.getCode();
            if (Objects.nonNull(saveNewsConfigDto)) {
                //上限分数
                Integer points = saveNewsConfigDto.getEveryDayMaxPoints();
                Integer sumPoints = glBusinessPointsLogDao.queryUserReadPoints(readType, today, glUser.getId());
                int currentPoints = sumPoints == null ? 0 : sumPoints;
                if (currentPoints <= points) {
                    GlBusinessPointsLogDO glBusinessPointsLogDO = new GlBusinessPointsLogDO();
                    glBusinessPointsLogDO.setUserId(glUser.getId());
                    glBusinessPointsLogDO.setCreated(today);
                    glBusinessPointsLogDO.setBusinessType(UserPointsSettingEnum.READ_USER.getCode());
                    glBusinessPointsLogDO.setBusinessKey(key);
                    glBusinessPointsLogDO.setPoints(saveNewsConfigDto.getEveryDayNews());
                    try {
                        glBusinessPointsLogDao.insert(glBusinessPointsLogDO);
                    }catch (DuplicateKeyException  e){
                        log.warn("重复key:{},created:{}",key,today);
                    }catch (Exception e) {
                        log.error("sendUserReadPoints", e);
                        continue;
                    }
                    //发放积分
                    glPointsService.sendUserReadPoints(glBusinessPointsLogDO, glUser.getMobileSha256(),localDateTime);
                }
            }
        }
        FileUtil.writeUtf8Lines(logList,"D:/process-1.log");
    }
}