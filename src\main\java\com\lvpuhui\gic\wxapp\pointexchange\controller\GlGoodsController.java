package com.lvpuhui.gic.wxapp.pointexchange.controller;

import com.baomidou.mybatisplus.extension.api.ApiController;
import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.infrastructure.utils.PassToken;
import com.lvpuhui.gic.wxapp.pointexchange.constant.RollbackConstant;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.pointexchange.dto.*;
import com.lvpuhui.gic.wxapp.pointexchange.service.GlGoodsService;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.my.service.GlUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品服务
 * <AUTHOR>
 * @since 2022年05月06日 14:25:00
 */
@RestController
public class GlGoodsController extends ApiController {

    @Autowired
    private GlGoodsService glGoodsService;

    @Autowired
    private GlUserService glUserService;

    @Autowired
    private GlPointsService glPointsService;

    /**
     * 商品列表接口-lijianguang
     */
    @PassToken
    @GetMapping("/goods")
    public R<List<Goods>> goods(){
        List<Goods> goods = glGoodsService.goods();
        return success(goods);
    }

    /**
     * 商品详情接口-lijianguang
     */
    @PassToken
    @GetMapping("/goods_detail")
    public R<GoodsDetail> goodsDetail(@RequestParam("id") Long id){
        GoodsDetailDto goodsDetailDto = new GoodsDetailDto();
        goodsDetailDto.setId(id);
        GoodsDetail goodsDetail = glGoodsService.goodsDetail(goodsDetailDto);
        return success(goodsDetail);
    }

    /**
     * 兑换商品接口-lijianguang
     */
    @PostMapping("/goods_exchange")
    public R<GoodsExchange> goodsExchange(@RequestBody @Valid GoodsExchangeDto goodsExchangeDto){
        try {
            GoodsExchange goodsExchange = glGoodsService.goodsExchange(goodsExchangeDto);
            return success(goodsExchange);
        }catch (GicWxAppException e){
            if(e.getMessage().equals(RollbackConstant.INSUFFICIENT_INVENTORY)
                    || e.getMessage().equals(RollbackConstant.INSUFFICIENT_POINT)){
                glPointsService.calcUserPoints(goodsExchangeDto.getMobileSha256());
            }
            if(e.getMessage().equals(RollbackConstant.INSUFFICIENT_POINT_V2)){
                glPointsService.calcUserPoints(goodsExchangeDto.getMobileSha256());
                throw new GicWxAppException(RollbackConstant.INSUFFICIENT_POINT);
            }
            throw e;
        }
    }

    /**
     * 我的绿色积分接口-lijianguang
     */
    @GetMapping("/point_data")
    public R<PointData> pointData(@RequestParam("mobileSha256") String mobileSha256){
        PointDataDto pointDataDto = new PointDataDto();
        pointDataDto.setMobileSha256(mobileSha256);
        PointData pointData = glUserService.pointData(pointDataDto);
        return success(pointData);
    }

    /**
     * 已兑换商品列表接口-lijianguang
     */
    @GetMapping("/goods_used")
    public R<List<GoodsUsed>> goodsUsed(@RequestParam("mobileSha256") String mobileSha256){
        GoodsUsedDto goodsUsedDto = new GoodsUsedDto();
        goodsUsedDto.setMobileSha256(mobileSha256);
        List<GoodsUsed> goodsUseds = glGoodsService.goodsUsed(goodsUsedDto);
        return success(goodsUseds);
    }

    /**
     * 已兑换商品详情接口-lijianguang
     */
    @GetMapping("/goods_detail_used")
    public R<GoodsExchangedDetail> goodsDetailUsed(@RequestParam("id") Long id){
        GoodsExchangedDetailDto goodsExchangedDetailDto = new GoodsExchangedDetailDto();
        goodsExchangedDetailDto.setId(id);
        GoodsExchangedDetail goodsExchangedDetail = glGoodsService.goodsDetailUsed(goodsExchangedDetailDto);
        return success(goodsExchangedDetail);
    }

    @GetMapping("/proGoods")
    public R<String> proGoods(){
        glGoodsService.proGoods();
        return success("s");
    }
}
