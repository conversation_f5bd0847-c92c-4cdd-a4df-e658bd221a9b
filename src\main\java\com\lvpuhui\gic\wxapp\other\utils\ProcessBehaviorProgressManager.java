package com.lvpuhui.gic.wxapp.other.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lvpuhui.gic.wxapp.other.dao.ProcessBehaviorProgressDao;
import com.lvpuhui.gic.wxapp.other.dao.ProcessBehaviorTableProgressDao;
import com.lvpuhui.gic.wxapp.other.entity.ProcessBehaviorProgress;
import com.lvpuhui.gic.wxapp.other.entity.ProcessBehaviorTableProgress;
import com.lvpuhui.gic.wxapp.other.vo.ProcessProgressVo;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 处理进度管理器
 * <AUTHOR>
 */
@Slf4j
public class ProcessBehaviorProgressManager {

    private final ProcessBehaviorProgressDao progressDao;
    private final ProcessBehaviorTableProgressDao tableProgressDao;
    private final TableQueueManager queueManager;
    
    private ProcessBehaviorProgress currentProgress;
    private final Map<String, ProcessBehaviorTableProgress> tableProgressMap;
    
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalGrantedPoints = new AtomicLong(0);
    private final AtomicLong totalDuplicateCount = new AtomicLong(0);
    
    private volatile Date startTime;
    private volatile Date lastUpdateTime;

    public ProcessBehaviorProgressManager(ProcessBehaviorProgressDao progressDao,
                                        ProcessBehaviorTableProgressDao tableProgressDao,
                                        TableQueueManager queueManager) {
        this.progressDao = progressDao;
        this.tableProgressDao = tableProgressDao;
        this.queueManager = queueManager;
        this.tableProgressMap = new HashMap<>();
        this.startTime = new Date();
        this.lastUpdateTime = new Date();
    }

    /**
     * 初始化或恢复进度
     */
    public void initializeProgress() {
        // 查找最新的进度记录
        currentProgress = progressDao.selectOne(
            new LambdaQueryWrapper<ProcessBehaviorProgress>()
                .orderByDesc(ProcessBehaviorProgress::getId)
                .last("LIMIT 1")
        );
        
        if (currentProgress == null) {
            // 创建新的进度记录
            currentProgress = new ProcessBehaviorProgress()
                .setLastProcessedId(0L)
                .setTotalProcessed(0L)
                .setTotalGrantedPoints(0L)
                .setDuplicateCount(0L)
                .setStatus("RUNNING")
                .setCreatedTime(new Date())
                .setUpdatedTime(new Date());
            progressDao.insert(currentProgress);
        } else {
            // 恢复状态
            totalProcessed.set(currentProgress.getTotalProcessed());
            totalGrantedPoints.set(currentProgress.getTotalGrantedPoints());
            totalDuplicateCount.set(currentProgress.getDuplicateCount());
            
            // 更新状态为运行中
            currentProgress.setStatus("RUNNING").setUpdatedTime(new Date());
            progressDao.updateById(currentProgress);
        }
        
        // 加载分表进度
        loadTableProgress();
        
        log.info("进度管理器初始化完成，从ID {} 开始处理", currentProgress.getLastProcessedId());
    }

    /**
     * 加载分表进度
     */
    private void loadTableProgress() {
        List<ProcessBehaviorTableProgress> tableProgressList = tableProgressDao.selectList(null);
        for (ProcessBehaviorTableProgress tableProgress : tableProgressList) {
            tableProgressMap.put(tableProgress.getTableSuffix(), tableProgress);
        }
        
        // 初始化缺失的分表进度记录
        String[] hexArray = {"0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"};
        for (String i : hexArray) {
            for (String j : hexArray) {
                String suffix = i + j;
                if (!tableProgressMap.containsKey(suffix)) {
                    ProcessBehaviorTableProgress tableProgress = new ProcessBehaviorTableProgress()
                        .setTableSuffix(suffix)
                        .setProcessedCount(0L)
                        .setGrantedPointsCount(0L)
                        .setDuplicateCount(0L)
                        .setLastUpdated(new Date());
                    tableProgressDao.insert(tableProgress);
                    tableProgressMap.put(suffix, tableProgress);
                }
            }
        }
    }

    /**
     * 更新主进度
     */
    public void updateMainProgress(Long lastProcessedId) {
        currentProgress.setLastProcessedId(lastProcessedId)
            .setTotalProcessed(totalProcessed.get())
            .setTotalGrantedPoints(totalGrantedPoints.get())
            .setDuplicateCount(totalDuplicateCount.get())
            .setUpdatedTime(new Date());
        
        progressDao.updateById(currentProgress);
        lastUpdateTime = new Date();
    }

    /**
     * 更新分表进度
     */
    public void updateTableProgress(String tableSuffix, long processedCount, long grantedPointsCount, long duplicateCount) {
        ProcessBehaviorTableProgress tableProgress = tableProgressMap.get(tableSuffix);
        if (tableProgress != null) {
            tableProgress.setProcessedCount(tableProgress.getProcessedCount() + processedCount)
                .setGrantedPointsCount(tableProgress.getGrantedPointsCount() + grantedPointsCount)
                .setDuplicateCount(tableProgress.getDuplicateCount() + duplicateCount)
                .setLastUpdated(new Date());
            
            tableProgressDao.updateById(tableProgress);
            
            // 更新总计数器
            totalProcessed.addAndGet(processedCount);
            totalGrantedPoints.addAndGet(grantedPointsCount);
            totalDuplicateCount.addAndGet(duplicateCount);
        }
    }

    /**
     * 获取当前进度信息
     */
    public ProcessProgressVo getCurrentProgress() {
        ProcessProgressVo vo = new ProcessProgressVo()
            .setStatus(currentProgress.getStatus())
            .setLastProcessedId(currentProgress.getLastProcessedId())
            .setTotalProcessed(totalProcessed.get())
            .setTotalGrantedPoints(totalGrantedPoints.get())
            .setDuplicateCount(totalDuplicateCount.get())
            .setStartTime(startTime)
            .setLastUpdated(lastUpdateTime)
            .setQueueBacklog(queueManager.getAllQueueSizes());
        
        // 计算处理速度
        long elapsedSeconds = (System.currentTimeMillis() - startTime.getTime()) / 1000;
        if (elapsedSeconds > 0) {
            vo.setProcessSpeed((double) totalProcessed.get() / elapsedSeconds);
        }
        
        // 设置分表进度信息
        Map<String, ProcessProgressVo.TableProgressInfo> tableProgressInfo = new HashMap<>();
        for (Map.Entry<String, ProcessBehaviorTableProgress> entry : tableProgressMap.entrySet()) {
            ProcessBehaviorTableProgress progress = entry.getValue();
            ProcessProgressVo.TableProgressInfo info = new ProcessProgressVo.TableProgressInfo()
                .setTableSuffix(progress.getTableSuffix())
                .setProcessedCount(progress.getProcessedCount())
                .setGrantedPointsCount(progress.getGrantedPointsCount())
                .setDuplicateCount(progress.getDuplicateCount())
                .setLastUpdated(progress.getLastUpdated());
            tableProgressInfo.put(entry.getKey(), info);
        }
        vo.setTableProgress(tableProgressInfo);
        
        return vo;
    }

    /**
     * 暂停处理
     */
    public void pauseProcess() {
        currentProgress.setStatus("PAUSED").setUpdatedTime(new Date());
        progressDao.updateById(currentProgress);
        log.info("处理已暂停");
    }

    /**
     * 恢复处理
     */
    public void resumeProcess() {
        currentProgress.setStatus("RUNNING").setUpdatedTime(new Date());
        progressDao.updateById(currentProgress);
        log.info("处理已恢复");
    }

    /**
     * 完成处理
     */
    public void completeProcess() {
        currentProgress.setStatus("COMPLETED").setUpdatedTime(new Date());
        progressDao.updateById(currentProgress);
        log.info("处理已完成");
    }

    /**
     * 处理失败
     */
    public void failProcess(String errorMessage) {
        currentProgress.setStatus("FAILED").setUpdatedTime(new Date());
        progressDao.updateById(currentProgress);
        log.error("处理失败: {}", errorMessage);
    }

    /**
     * 获取最后处理的ID
     */
    public Long getLastProcessedId() {
        return currentProgress.getLastProcessedId();
    }

    /**
     * 检查是否暂停
     */
    public boolean isPaused() {
        return "PAUSED".equals(currentProgress.getStatus());
    }
}
