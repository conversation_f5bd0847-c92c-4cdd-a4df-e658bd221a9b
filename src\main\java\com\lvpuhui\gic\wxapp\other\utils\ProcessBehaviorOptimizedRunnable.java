package com.lvpuhui.gic.wxapp.other.utils;

import com.lvpuhui.gic.wxapp.carbonbook.dao.GlBehaviorDao;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorTmpService;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.other.dao.ProcessBehaviorProgressDao;
import com.lvpuhui.gic.wxapp.other.dao.ProcessBehaviorTableProgressDao;
import com.lvpuhui.gic.wxapp.other.service.DataHandleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 优化版绿色行为处理器
 * <AUTHOR>
 */
@Slf4j
public class ProcessBehaviorOptimizedRunnable implements Runnable {

    private static final int THREAD_COUNT = 6;
    private static final int WRITE_SEMAPHORE_PERMITS = 3;
    private static final String[] HEX_ARRAY = {"0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"};
    
    private final AtomicBoolean stopped = new AtomicBoolean(false);
    
    private final GlPointsService glPointsService;
    private final GlBehaviorTmpService glBehaviorTmpService;
    private final GlBehaviorDao glBehaviorDao;
    private final double rate;
    private final DataHandleService dataHandleService;
    private final ProcessBehaviorProgressDao progressDao;
    private final ProcessBehaviorTableProgressDao tableProgressDao;
    
    // 核心组件
    private TableQueueManager queueManager;
    private ProcessBehaviorProgressManager progressManager;
    private DataReaderThread dataReaderThread;
    private List<TableProcessorThread> processorThreads;
    private Semaphore writeSemaphore;
    
    // 线程管理
    private Thread readerThread;
    private List<Thread> processorThreadList;

    public ProcessBehaviorOptimizedRunnable(GlPointsService glPointsService,
                                          GlBehaviorTmpService glBehaviorTmpService,
                                          GlBehaviorDao glBehaviorDao,
                                          double rate,
                                          DataHandleService dataHandleService,
                                          ProcessBehaviorProgressDao progressDao,
                                          ProcessBehaviorTableProgressDao tableProgressDao) {
        this.glPointsService = glPointsService;
        this.glBehaviorTmpService = glBehaviorTmpService;
        this.glBehaviorDao = glBehaviorDao;
        this.rate = rate;
        this.dataHandleService = dataHandleService;
        this.progressDao = progressDao;
        this.tableProgressDao = tableProgressDao;
    }

    @Override
    public void run() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        
        log.info("优化版绿色行为处理器启动");
        
        try {
            // 初始化组件
            initializeComponents();
            
            // 启动处理线程
            startProcessingThreads();
            
            // 等待所有线程完成
            waitForCompletion();
            
            // 最终处理
            finalizeProcessing();
            
        } catch (Exception e) {
            log.error("处理器运行异常", e);
            progressManager.failProcess("处理器运行异常: " + e.getMessage());
        } finally {
            // 清理资源
            cleanup();
        }
        
        stopWatch.stop();
        log.info("优化版绿色行为处理器完成，总耗时: {} 秒", stopWatch.getTotalTimeSeconds());
    }

    /**
     * 初始化组件
     */
    private void initializeComponents() {
        log.info("初始化组件...");
        
        // 初始化队列管理器
        queueManager = new TableQueueManager();
        
        // 初始化进度管理器
        progressManager = new ProcessBehaviorProgressManager(progressDao, tableProgressDao, queueManager);
        progressManager.initializeProgress();
        
        // 初始化写入信号量
        writeSemaphore = new Semaphore(WRITE_SEMAPHORE_PERMITS);
        
        // 初始化数据读取线程
        dataReaderThread = new DataReaderThread(glBehaviorTmpService, queueManager, progressManager);
        
        // 初始化分表处理线程
        initializeProcessorThreads();
        
        log.info("组件初始化完成");
    }

    /**
     * 初始化分表处理线程
     */
    private void initializeProcessorThreads() {
        processorThreads = new ArrayList<>();
        
        // 生成所有分表后缀
        List<String> allTableSuffixes = new ArrayList<>();
        for (String i : HEX_ARRAY) {
            for (String j : HEX_ARRAY) {
                allTableSuffixes.add(i + j);
            }
        }
        
        // 将256个分表分配给6个线程
        int tablesPerThread = allTableSuffixes.size() / THREAD_COUNT;
        int remainder = allTableSuffixes.size() % THREAD_COUNT;
        
        int startIndex = 0;
        for (int i = 0; i < THREAD_COUNT; i++) {
            int endIndex = startIndex + tablesPerThread + (i < remainder ? 1 : 0);
            List<String> assignedTables = allTableSuffixes.subList(startIndex, endIndex);
            
            TableProcessorThread processorThread = new TableProcessorThread(
                i + 1,
                assignedTables,
                queueManager,
                progressManager,
                glBehaviorDao,
                glPointsService,
                writeSemaphore,
                rate
            );
            
            processorThreads.add(processorThread);
            startIndex = endIndex;
            
            log.info("线程 {} 分配 {} 个分表: {} - {}", 
                i + 1, assignedTables.size(), assignedTables.get(0), assignedTables.get(assignedTables.size() - 1));
        }
    }

    /**
     * 启动处理线程
     */
    private void startProcessingThreads() {
        log.info("启动处理线程...");
        
        // 启动数据读取线程
        readerThread = new Thread(dataReaderThread, "DataReader");
        readerThread.start();
        
        // 启动分表处理线程
        processorThreadList = new ArrayList<>();
        for (int i = 0; i < processorThreads.size(); i++) {
            Thread thread = new Thread(processorThreads.get(i), "TableProcessor-" + (i + 1));
            processorThreadList.add(thread);
            thread.start();
        }
        
        log.info("所有处理线程已启动");
    }

    /**
     * 等待所有线程完成
     */
    private void waitForCompletion() {
        try {
            // 等待数据读取线程完成
            readerThread.join();
            log.info("数据读取线程已完成");
            
            // 等待队列中的数据处理完成
            while (!queueManager.isEmpty() && !stopped.get()) {
                log.info("等待队列中剩余数据处理完成，剩余: {} 条", queueManager.getTotalQueueSize());
                Thread.sleep(5000);
            }
            
            // 停止所有处理线程
            for (TableProcessorThread processorThread : processorThreads) {
                processorThread.stop();
            }
            
            // 等待所有处理线程完成
            for (Thread thread : processorThreadList) {
                thread.join();
            }
            
            log.info("所有处理线程已完成");
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("等待线程完成时被中断", e);
        }
    }

    /**
     * 最终处理
     */
    private void finalizeProcessing() {
        try {
            // 发放用户积分
            glPointsService.grantUserPoints(null);
            
            // 调用统计方法
            dataHandleService.callStatisticsMethod();
            
            // 标记处理完成
            progressManager.completeProcess();
            
            log.info("最终处理完成");
            
        } catch (Exception e) {
            log.error("最终处理异常", e);
        }
    }

    /**
     * 清理资源
     */
    private void cleanup() {
        try {
            if (queueManager != null) {
                queueManager.clear();
            }
            log.info("资源清理完成");
        } catch (Exception e) {
            log.error("资源清理异常", e);
        }
    }

    /**
     * 停止处理
     */
    public void stop() {
        stopped.set(true);
        
        if (dataReaderThread != null) {
            dataReaderThread.stop();
        }
        
        if (processorThreads != null) {
            for (TableProcessorThread processorThread : processorThreads) {
                processorThread.stop();
            }
        }
        
        log.info("优化版处理器收到停止信号");
    }

    /**
     * 获取进度管理器
     */
    public ProcessBehaviorProgressManager getProgressManager() {
        return progressManager;
    }
}
