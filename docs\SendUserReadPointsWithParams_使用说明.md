# 用户阅读积分发放（带参数版本）使用说明

## 概述

新增了 `sendUserReadPointsWithParams` 方法，允许通过接口参数完全控制用户阅读积分发放的所有关键参数，而不依赖于当前登录用户信息和系统时间。

## 新增文件

### 1. DTO类
- `SendUserReadPointsWithParamsDto.java` - 包含所有参数的DTO类

### 2. 服务方法
- `GlUserService.sendUserReadPointsWithParams()` - 服务接口方法
- `GlUserServiceImpl.sendUserReadPointsWithParams()` - 服务实现方法

### 3. 控制器接口
- `POST /mine/sendUserReadPointsWithParams` - REST API接口

## 参数说明

### SendUserReadPointsWithParamsDto

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| today | LocalDate | 日期 | "2024-01-15" |
| time | LocalTime | 具体时间 | "14:30:00" |
| userId | Long | 用户ID | 12345 |
| mobile | String | 手机号SHA256 | "abc123..." |
| newId | Long | 新闻ID | 67890 |
| points | Integer | 积分数（可选） | 10 |

## 与原方法的对比

### 原方法 (sendUserReadPoints)
```java
// 从当前上下文获取
LocalDate today = LocalDate.now();
Long userId = UserUtils.getUserId();
String mobile = UserUtils.getMobileSha256();
// 时间固定为当前时间
LocalDateTime.now()
```

### 新方法 (sendUserReadPointsWithParams)
```java
// 从参数获取
LocalDate today = dto.getToday();
Long userId = dto.getUserId();
String mobile = dto.getMobile();
// 时间由参数组合
LocalDateTime localDateTime = LocalDateTime.of(dto.getToday(), dto.getTime());
```

## API调用示例

### 请求
```http
POST /mine/sendUserReadPointsWithParams
Content-Type: application/json

{
    "today": "2024-01-15",
    "time": "14:30:00",
    "userId": 12345,
    "mobile": "abc123def456...",
    "newId": 67890,
    "points": 10
}
```

### 响应
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": 10
}
```

## 业务逻辑

1. **参数验证**：使用传入的参数替代原来的系统获取方式
2. **积分限制检查**：基于传入的日期和用户ID检查当日积分上限
3. **记录创建**：使用传入的参数创建业务积分记录
4. **积分发放**：使用传入的日期和时间组合发放积分

## 使用场景

1. **批量数据处理**：可以指定具体的用户和时间进行积分发放
2. **历史数据补录**：可以补录指定日期的积分记录
3. **测试场景**：可以模拟不同用户和时间的积分发放
4. **数据迁移**：从其他系统迁移积分数据时使用

## 注意事项

1. **向后兼容**：原有的方法保持不变，不影响现有功能
2. **参数完整性**：所有必要参数都需要从接口传入
3. **时间组合**：日期和时间会组合成完整的LocalDateTime传给积分服务
4. **业务规则**：仍然遵循原有的积分发放业务规则（如每日上限等）

## 安全考虑

由于该方法允许指定任意用户ID和手机号，建议：
1. 添加适当的权限控制
2. 记录操作日志
3. 验证参数的合法性

## 测试建议

1. 验证参数传递的正确性
2. 测试不同日期和时间的组合
3. 确认积分发放的准确性
4. 验证业务规则的执行
