package com.lvpuhui.gic.wxapp.other.utils;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.lvpuhui.gic.wxapp.carbonbook.dao.GlBehaviorDao;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehavior;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehaviorTmp;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.infrastructure.sharding.DynamicTableNameHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Semaphore;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 分表处理线程
 * <AUTHOR>
 */
@Slf4j
public class TableProcessorThread implements Runnable {

    private static final int PROCESS_BATCH_SIZE = 300;
    private static final int PROCESS_DELAY_MS = 75;
    
    private final int threadId;
    private final List<String> assignedTableSuffixes;
    private final TableQueueManager queueManager;
    private final ProcessBehaviorProgressManager progressManager;
    private final GlBehaviorDao glBehaviorDao;
    private final GlPointsService glPointsService;
    private final Semaphore writeSemaphore;
    private final double rate;
    
    private final AtomicBoolean stopped = new AtomicBoolean(false);

    public TableProcessorThread(int threadId,
                               List<String> assignedTableSuffixes,
                               TableQueueManager queueManager,
                               ProcessBehaviorProgressManager progressManager,
                               GlBehaviorDao glBehaviorDao,
                               GlPointsService glPointsService,
                               Semaphore writeSemaphore,
                               double rate) {
        this.threadId = threadId;
        this.assignedTableSuffixes = assignedTableSuffixes;
        this.queueManager = queueManager;
        this.progressManager = progressManager;
        this.glBehaviorDao = glBehaviorDao;
        this.glPointsService = glPointsService;
        this.writeSemaphore = writeSemaphore;
        this.rate = rate;
    }

    @Override
    public void run() {
        log.info("分表处理线程 {} 启动，负责处理 {} 个分表", threadId, assignedTableSuffixes.size());
        
        try {
            while (!stopped.get()) {
                // 检查是否暂停
                if (progressManager.isPaused()) {
                    Thread.sleep(1000);
                    continue;
                }
                
                boolean hasData = false;
                
                // 轮询分配的队列
                for (String tableSuffix : assignedTableSuffixes) {
                    if (stopped.get()) {
                        break;
                    }
                    
                    List<GlBehaviorTmp> dataList = queueManager.pollBatch(tableSuffix, PROCESS_BATCH_SIZE);
                    if (!dataList.isEmpty()) {
                        hasData = true;
                        processTableData(tableSuffix, dataList);
                        
                        // 处理延迟
                        Thread.sleep(PROCESS_DELAY_MS);
                    }
                }
                
                // 如果没有数据，稍微等待一下
                if (!hasData) {
                    Thread.sleep(100);
                }
            }
            
        } catch (Exception e) {
            log.error("分表处理线程 {} 异常", threadId, e);
            progressManager.failProcess("分表处理线程异常: " + e.getMessage());
        }
        
        log.info("分表处理线程 {} 结束", threadId);
    }

    /**
     * 处理单个分表的数据
     */
    private void processTableData(String tableSuffix, List<GlBehaviorTmp> dataList) {
        try {
            // 获取写入信号量
            writeSemaphore.acquire();
            
            try {
                // 转换数据
                List<GlBehavior> behaviorList = convertToBehaviorList(dataList);
                
                // 批量插入行为数据
                long insertedCount = batchInsertBehaviors(tableSuffix, behaviorList);
                
                // 批量检查和发放积分
                long grantedPointsCount = batchGrantPoints(tableSuffix, behaviorList);
                
                // 更新分表进度
                long duplicateCount = dataList.size() - insertedCount;
                progressManager.updateTableProgress(tableSuffix, insertedCount, grantedPointsCount, duplicateCount);
                
                log.debug("线程 {} 处理分表 {} 完成，插入 {} 条，发放积分 {} 条，重复 {} 条", 
                    threadId, tableSuffix, insertedCount, grantedPointsCount, duplicateCount);
                
            } finally {
                writeSemaphore.release();
            }
            
        } catch (Exception e) {
            log.error("处理分表 {} 数据异常", tableSuffix, e);
        }
    }

    /**
     * 转换数据格式
     */
    private List<GlBehavior> convertToBehaviorList(List<GlBehaviorTmp> dataList) {
        List<GlBehavior> behaviorList = new ArrayList<>();
        
        for (GlBehaviorTmp tmp : dataList) {
            GlBehavior behavior = new GlBehavior();
            behavior.setId(IdWorker.getId());
            behavior.setEventId(tmp.getEventId());
            behavior.setRegion(tmp.getRegion());
            behavior.setTenantId(tmp.getTenantId().longValue());
            behavior.setAppId(tmp.getAppId());
            behavior.setScenarioId(tmp.getScenarioId());
            behavior.setBehaviorId(tmp.getActId());
            behavior.setMobileSha256(tmp.getMobileSha256());
            behavior.setDate(tmp.getDate());
            behavior.setEmission(tmp.getEmission());
            behavior.setCreated(tmp.getCreated());
            behavior.setType(0);
            behavior.setDeleted(0);
            behaviorList.add(behavior);
        }
        
        return behaviorList;
    }

    /**
     * 批量插入行为数据
     */
    private long batchInsertBehaviors(String tableSuffix, List<GlBehavior> behaviorList) {
        long insertedCount = 0;
        
        try {
            DynamicTableNameHolder.set("gl_behavior_" + tableSuffix);
            
            for (GlBehavior behavior : behaviorList) {
                try {
                    glBehaviorDao.insert(behavior);
                    insertedCount++;
                } catch (DuplicateKeyException e) {
                    log.debug("重复记录，tenantId: {}, eventId: {}", behavior.getTenantId(), behavior.getEventId());
                } catch (Exception e) {
                    log.error("插入行为数据异常", e);
                }
            }
            
        } finally {
            DynamicTableNameHolder.remove();
        }
        
        return insertedCount;
    }

    /**
     * 批量检查和发放积分
     */
    private long batchGrantPoints(String tableSuffix, List<GlBehavior> behaviorList) {
        long grantedCount = 0;
        
        try {
            // 批量检查积分是否已存在
            List<String> eventIds = behaviorList.stream()
                .map(GlBehavior::getEventId)
                    .collect(Collectors.toList());
            
            List<String> existingEventIds = batchCheckExistingPoints(tableSuffix, eventIds);
            
            // 过滤已存在积分的记录
            List<GlBehavior> needGrantPoints = behaviorList.stream()
                .filter(b -> !existingEventIds.contains(b.getEventId()))
                    .collect(Collectors.toList());
            
            // 批量发放积分
            for (GlBehavior behavior : needGrantPoints) {
                try {
                    glPointsService.grantBehaviorPoint(behavior, rate);
                    grantedCount++;
                } catch (Exception e) {
                    log.error("发放积分异常，eventId: {}", behavior.getEventId(), e);
                }
            }
            
            if (existingEventIds.size() > 0) {
                log.debug("分表 {} 发现 {} 条重复积分记录", tableSuffix, existingEventIds.size());
            }
            
        } catch (Exception e) {
            log.error("批量发放积分异常", e);
        }
        
        return grantedCount;
    }

    /**
     * 批量检查积分是否存在
     */
    private List<String> batchCheckExistingPoints(String tableSuffix, List<String> eventIds) {
        try {
            return glPointsService.batchCheckExistingEventIds(eventIds, tableSuffix);
        } catch (Exception e) {
            log.error("批量检查积分异常，tableSuffix: {}", tableSuffix, e);
            return new ArrayList<>();
        }
    }

    /**
     * 停止线程
     */
    public void stop() {
        stopped.set(true);
        log.info("分表处理线程 {} 收到停止信号", threadId);
    }

    /**
     * 检查是否已停止
     */
    public boolean isStopped() {
        return stopped.get();
    }
}
