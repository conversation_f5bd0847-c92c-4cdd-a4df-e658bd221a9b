package com.lvpuhui.gic.wxapp.my.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户阅读积分发放DTO（带参数版本）
 * <AUTHOR>
 */
@Data
@Schema(description = "用户阅读积分发放DTO（带参数版本）")
public class SendUserReadPointsWithParamsDto {

    @Schema(description = "具体时间")
    private LocalDateTime time;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "手机号SHA256")
    private String mobile;

    @Schema(description = "新闻ID")
    private Long newId;

    @Schema(description = "积分数")
    private Integer points;
}
