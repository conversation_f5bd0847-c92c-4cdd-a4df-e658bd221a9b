package com.lvpuhui.gic.wxapp.other.service.impl;

import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.carbonbook.dao.GlBehaviorDao;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorTmpService;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.other.dao.ProcessBehaviorProgressDao;
import com.lvpuhui.gic.wxapp.other.dao.ProcessBehaviorTableProgressDao;
import com.lvpuhui.gic.wxapp.other.service.DataHandleService;
import com.lvpuhui.gic.wxapp.other.service.ProcessBehaviorProgressService;
import com.lvpuhui.gic.wxapp.other.utils.ProcessBehaviorOptimizedRunnable;
import com.lvpuhui.gic.wxapp.other.vo.ProcessProgressVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 绿色行为处理进度服务实现
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProcessBehaviorProgressServiceImpl implements ProcessBehaviorProgressService {

    @Resource
    private GlPointsService glPointsService;
    
    @Resource
    private GlBehaviorTmpService glBehaviorTmpService;
    
    @Resource
    private GlBehaviorDao glBehaviorDao;
    
    @Resource
    private GlAppletConfigService glAppletConfigService;
    
    @Resource
    private DataHandleService dataHandleService;
    
    @Resource
    private ProcessBehaviorProgressDao progressDao;
    
    @Resource
    private ProcessBehaviorTableProgressDao tableProgressDao;

    private ProcessBehaviorOptimizedRunnable currentProcessor;
    private Thread processorThread;

    @Override
    public void startOptimizedProcessor() {
        if (isRunning()) {
            log.warn("处理器已在运行中，无法重复启动");
            return;
        }

        // 停止之前的处理器
        if (Objects.nonNull(currentProcessor)) {
            currentProcessor.stop();
        }

        // 获取积分转换比率
        double rate = glAppletConfigService.getBehaviorToPoints();

        // 创建新的处理器
        currentProcessor = new ProcessBehaviorOptimizedRunnable(
            glPointsService,
            glBehaviorTmpService,
            glBehaviorDao,
            rate,
            dataHandleService,
            progressDao,
            tableProgressDao
        );

        // 启动处理线程
        processorThread = new Thread(currentProcessor, "OptimizedBehaviorProcessor");
        processorThread.start();

        log.info("优化版绿色行为处理器已启动");
    }

    @Override
    public ProcessProgressVo getCurrentProgress() {
        if (currentProcessor == null || currentProcessor.getProgressManager() == null) {
            // 返回默认进度信息
            return new ProcessProgressVo()
                .setStatus("NOT_STARTED")
                .setTotalProcessed(0L)
                .setTotalGrantedPoints(0L)
                .setDuplicateCount(0L);
        }

        return currentProcessor.getProgressManager().getCurrentProgress();
    }

    @Override
    public void pauseProcess() {
        if (currentProcessor != null && currentProcessor.getProgressManager() != null) {
            currentProcessor.getProgressManager().pauseProcess();
            log.info("处理器已暂停");
        } else {
            log.warn("没有正在运行的处理器可以暂停");
        }
    }

    @Override
    public void resumeProcess() {
        if (currentProcessor != null && currentProcessor.getProgressManager() != null) {
            currentProcessor.getProgressManager().resumeProcess();
            log.info("处理器已恢复");
        } else {
            log.warn("没有正在运行的处理器可以恢复");
        }
    }

    @Override
    public void stopProcess() {
        if (currentProcessor != null) {
            currentProcessor.stop();
            log.info("处理器停止信号已发送");
        } else {
            log.warn("没有正在运行的处理器可以停止");
        }
    }

    @Override
    public boolean isRunning() {
        return processorThread != null && processorThread.isAlive();
    }
}
