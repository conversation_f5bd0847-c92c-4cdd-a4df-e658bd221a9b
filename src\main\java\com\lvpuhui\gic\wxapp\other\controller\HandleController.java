package com.lvpuhui.gic.wxapp.other.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.api.R;
import com.lvpuhui.gic.wxapp.carbonbook.dto.TmpDateBetweenDto;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorService;
import com.lvpuhui.gic.wxapp.infrastructure.utils.PassToken;
import com.lvpuhui.gic.wxapp.other.dto.DataHandleDto;
import com.lvpuhui.gic.wxapp.other.service.DataHandleService;
import com.lvpuhui.gic.wxapp.other.service.GlConsumptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("")
@Slf4j
public class HandleController {

    @Resource
    GlConsumptionService glConsumptionService;
    @Resource
    GlBehaviorService glBehaviorService;
    @Resource
    DataHandleService dataHandleService;

    /**
     * 处理数据
     */
    @PassToken
    @PostMapping("/behavior")
    public R<String> processBehavior()  {
        glBehaviorService.processBehavior();
        return R.ok("");
    }

    @PassToken
    @PostMapping("/grant")
    public R<Integer> grant(Long fileId)  {
        return R.ok(glConsumptionService.grant(fileId));
    }

    @PassToken
    @PostMapping("/behavior1")
    public R<Integer> behavior(String created, Integer isHandlePoints)  {
        StopWatch sw = new StopWatch();
        sw.start();
        int k = glBehaviorService.handleBehaviorV2(created);
        sw.stop();
        log.info("执行绿色行为数据 耗时:{}",sw.getTotalTimeSeconds());
        if(isHandlePoints == null){
            isHandlePoints = 0;
        }
        if(isHandlePoints!=0){
            log.warn("isHandlePoints {} stop.",isHandlePoints);
            return R.ok(k);
        }
        ThreadUtil.execute(()->{
            String dateBetween = "";
            if(StrUtil.isBlank(created)){
                TmpDateBetweenDto tmpDateBetween = glBehaviorService.getTmpDateBetween();
                if(Objects.nonNull(tmpDateBetween)){
                    String minDate = DateUtil.beginOfDay(tmpDateBetween.getMinDate()).toStringDefaultTimeZone();
                    String maxDate = DateUtil.endOfDay(tmpDateBetween.getMaxDate()).toStringDefaultTimeZone();
                    dateBetween = minDate + "," + maxDate;
                }else {
                    String minDate = DateUtil.beginOfDay(DateUtil.yesterday()).toStringDefaultTimeZone();
                    String maxDate = DateUtil.endOfDay(DateUtil.yesterday()).toStringDefaultTimeZone();
                    dateBetween = minDate + "," + maxDate;
                }
            }
            String date = StrUtil.isBlank(created)?dateBetween:created;
            glBehaviorService.handlePointsV2(date);
            // 执行webConsole的数据统计方法
            dataHandleService.callStatisticsMethod();
        });
        return R.ok(k);
    }

    @PassToken
    @GetMapping("/handlerMobilePoints")
    public R<String> handlerMobilePoints() {
        glBehaviorService.handlerMobilePoints();
        return R.ok("ok");
    }

    @PassToken
    @GetMapping("/handlerMobilePointsDate")
    public R<String> handlerMobilePointsDate() {
        glBehaviorService.handlerMobilePointsDate();
        return R.ok("ok");
    }

    @PassToken
    @PostMapping("/handlerPoints")
    public R<String> handlerPoints(String created)  {
        // https://wcapi.ordostanpuhui.com:1443/handlerPoints?created=2020-05-01 00:00:00,2024-03-01 22:44:07
        new Thread(() -> {
            glBehaviorService.handlePointsV2(StrUtil.isBlank(created)?
                    String.format("%s,%s",DateUtil.offsetDay(new Date(),-1)
                            .setField(Calendar.HOUR_OF_DAY,00)
                            .setField(Calendar.MINUTE,00)
                            .setField(Calendar.SECOND,00).toString(),DateUtil.offsetDay(new Date(),-1)
                            .setField(Calendar.HOUR_OF_DAY,23)
                            .setField(Calendar.MINUTE,59)
                            .setField(Calendar.SECOND,59).toString())
                    :created);
        }).start();
//        ThreadUtil.execute(()->{
//            glBehaviorService.handlePointsV2(StrUtil.isBlank(created)?
//                    String.format("%s,%s",DateUtil.offsetDay(new Date(),-1)
//                            .setField(Calendar.HOUR_OF_DAY,00)
//                            .setField(Calendar.MINUTE,00)
//                            .setField(Calendar.SECOND,00).toString(),DateUtil.offsetDay(new Date(),-1)
//                            .setField(Calendar.HOUR_OF_DAY,23)
//                            .setField(Calendar.MINUTE,59)
//                            .setField(Calendar.SECOND,59).toString())
//                    :created);
//            // 执行webConsole的数据统计方法
////            dataHandleService.callStatisticsMethod();
//        });
        return R.ok("ok");
    }

    @PostMapping("/calc_points")
    public R<String> calcPoints(String mobileSha256)  {
        glBehaviorService.calcPoints(mobileSha256);
        return R.ok("");
    }

    @PostMapping("/data/handle_behavior")
    public R<Long> handleBehavior(@RequestBody DataHandleDto dto)  {
        return R.ok(dataHandleService.handleBehavior(dto));
    }
    @PostMapping("/data/handle_points")
    public R<Long> handlePoints(@RequestBody DataHandleDto dto)  {
        return R.ok(dataHandleService.handlePoints(dto));
    }


    @PostMapping("/count/handle_behavior")
    public R<Long> countBehavior(String created)  {
        return R.ok(dataHandleService.countBehavior(created));
    }
    @PostMapping("/count/handle_points")
    public R<Long> countPoints(String created)  {
        return R.ok(dataHandleService.countPoints(created));
    }
    @PostMapping("/count/handle_consumption")
    public R<Long> countConsumption(String created)  {
        return R.ok(dataHandleService.countConsumption(created));
    }

}
