package com.lvpuhui.gic.wxapp.homepage.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lvpuhui.gic.wxapp.base.entity.GlAppletConfig;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehavior;
import com.lvpuhui.gic.wxapp.homepage.dao.GlPointsDao;
import com.lvpuhui.gic.wxapp.homepage.entity.*;
import com.lvpuhui.gic.wxapp.homepage.enums.PointsType;
import com.lvpuhui.gic.wxapp.homepage.service.GlCompanyService;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsIdService;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.homepage.service.GlTickoffTypeService;
import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import com.lvpuhui.gic.wxapp.infrastructure.exception.GicWxAppException;
import com.lvpuhui.gic.wxapp.my.entity.GlBusinessPointsLogDO;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.my.entity.GlUserFriends;
import com.lvpuhui.gic.wxapp.my.service.GlUserService;
import com.lvpuhui.gic.wxapp.other.entity.GlConsumption;
import com.lvpuhui.gic.wxapp.pointexchange.entity.GlGoods;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 积分流水表(GlPoints)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 13:45:10
 */
@Service("glPointsService")
@Slf4j
public class GlPointsServiceImpl extends ServiceImpl<GlPointsDao, GlPoints> implements GlPointsService {
    @Resource
    GlPointsIdService glPointsIdService;
    @Resource
    GlUserService glUserService;

    @Resource
    GlCompanyService glCompanyService;
    @Resource
    GlTickoffTypeService glTickoffTypeService;
    @Resource
    GlAppletConfigService glAppletConfigService;

    private JSONObject behaviorMap ;

    @PostConstruct
    public void init() {
        TimerTask timerTask = new TimerTask() {
            @Override
            public void run() {
                log.info("behaviorMap");
                GlAppletConfig behavior = glAppletConfigService.getOne(new LambdaQueryWrapper<GlAppletConfig>().eq(GlAppletConfig::getParamKey,
                        Global.CONFIG_BEHAVIOR).last(" LIMIT 1"));
                if(behavior != null){
                    behaviorMap = JSON.parseObject(behavior.getParamValue());
                }
            }
        };
        Timer timer = new Timer();
        timer.schedule(timerTask, 1000, 30 * 60 * 1000);
    }


    @Override
    public void grantConsumptionPoints(GlConsumption consumption, double points) {
        GlPoints p = new GlPoints();
        BeanUtil.copyProperties(consumption, p);
        long id = consumption.getId() == null ? 0: consumption.getId();
        int count = count(new LambdaQueryWrapper<GlPoints>().eq(GlPoints::getCompanyId, consumption.getCompanyId()).eq(GlPoints::getSourceId, id));
        if (count > 0) {
            log.error("已领取过积分 -> {}", JSON.toJSONString(consumption));
            return;
        }
        p.setType(PointsType.CONSUMPTION.getCode());
        p.setTableName("gl_consumption");
        p.setSourceId(consumption.getId());
        p.setOrderId(consumption.getOrderId());
        p.setDescription(consumption.getProductName());
        p.setId(null);
        p.setPoint(points);
        p.setCreated(new Date());
        save(p);
    }


    @Override
    public void grantBehaviorPoints(GlBehavior behavior, double rate) {
       grantBehaviorPoints(behavior,rate,PointsType.BEHAVIOR);
    }

    @Override
    public void grantBehaviorPoints(GlBehavior behavior, double rate, PointsType pointsType) {
        GlPoints p = new GlPoints();
        BeanUtil.copyProperties(behavior, p);
        long id = behavior.getId() == null ? 0: behavior.getId();
        p.setType(pointsType.getCode());
        GlCompany company = null;
        if(pointsType.getCode().equals(PointsType.LV_BEHAVIOR.getCode())){
            p.setDescription("绿享生活家电减排积分");
            company = glCompanyService.getCacheCompanyById(behavior.getTenantId().longValue());
        }else {
            p.setDescription(behaviorMap.getString(String.valueOf(behavior.getBehaviorId())));
            company = glCompanyService.getCacheCompanyByTenantId(behavior.getTenantId().longValue());
        }
        if(company == null){
            log.error("company empty tenantId:{}",behavior.getTenantId());
            return;
        }
        p.setTableName("gl_behavior");
        p.setSourceId(behavior.getId());
        p.setOrderId(behavior.getEventId());
        p.setId(null);
        p.setPoint(NumberUtil.round(NumberUtil.mul(behavior.getEmission().doubleValue(),rate),
                0, RoundingMode.UP).doubleValue());
        p.setCompanyId(company.getId());
        p.setCompanyName(company.getName());
        int count = count(new LambdaQueryWrapper<GlPoints>()
                .eq(GlPoints::getMobileSha256,p.getMobileSha256())
                .eq(GlPoints::getCompanyId, p.getCompanyId())
                .eq(GlPoints::getSourceId, id));
        if (count > 0) {
            log.error("已领取过积分 -> {}", JSON.toJSONString(behavior));
            return;
        }
        p.setCreated(new Date());
        save(p);
    }

    @Override
    public void consumedPoints(GlGoods goods, String mobileSha256) {
        GlPoints p = new GlPoints();
        BeanUtil.copyProperties(goods, p,"type");
        p.setType(PointsType.EXCHANGE.getCode());
        p.setTableName("gl_goods");
        p.setSourceId(goods.getId());
        p.setDescription(goods.getName());
        p.setOrderId("");
        p.setId(null);
        p.setPoint(-goods.getPoint().doubleValue());
        p.setCompanyId(goods.getCompanyId().longValue());
        p.setCompanyName(goods.getCompanyName());
        p.setCreated(new Date());
        p.setMobileSha256(mobileSha256);
        save(p);
        calcUserPoints(p.getMobileSha256());
    }


    @Override
    public void calcUserPoints(String mobileSha256) {
        GlUser user = glUserService.getOne(new LambdaQueryWrapper<GlUser>().eq(GlUser::getMobileSha256,mobileSha256));
        if(user ==null){
//            throw new GicWxAppException("user empty mobileSha256:"+mobileSha256);
            return;
        }
        List<GlPoints> points = list(new LambdaQueryWrapper<GlPoints>().select(GlPoints::getPoint,GlPoints::getType)
                .eq(GlPoints::getMobileSha256,mobileSha256));
        long pointTotal =0L;
        long pointRemain=0L;
        long pointConsumed=0L;
        int obtainTimes = 0;
        int exchangeTimes =0;
        if (!CollectionUtil.isEmpty(points)) {
            for(GlPoints p : points){
                if(p.getType().equals(PointsType.EXCHANGE.getCode())){
                    exchangeTimes++;
                    pointConsumed +=p.getPoint().intValue();
                }else {
                    pointTotal+=p.getPoint().intValue();
                    obtainTimes++;
                }
                pointRemain+=p.getPoint().intValue();
            }
        }
        if(pointRemain<0){
            throw new GicWxAppException("pointRemain<0");
        }
        glUserService.update(new LambdaUpdateWrapper<GlUser>()
                .set(GlUser::getObtainTimes,obtainTimes)
                .set(GlUser::getExchangeTimes,exchangeTimes)
                .set(GlUser::getPointTotal,pointTotal)
                .set(GlUser::getPointRemain,pointRemain)
                .set(GlUser::getPointConsumed,pointConsumed)
                .set(GlUser::getUpdated,new Date())
                .eq(GlUser::getId,user.getId()));
    }

    @Override
    public void grantVideo(GlVideoRecord glVideoRecord,Long companyId,String companyName,String mobileSha256,Long videoId) {
        GlPoints p = new GlPoints();
        BeanUtil.copyProperties(glVideoRecord, p);
        p.setType(PointsType.VIDEO.getCode());
        p.setTableName("gl_video_record");
        p.setSourceId(glVideoRecord.getId());
        p.setDescription("");
        p.setOrderId(videoId.toString());
        p.setId(null);
        p.setPoint(glVideoRecord.getPoint().doubleValue());
        p.setCompanyId(companyId);
        p.setCompanyName(companyName);
        p.setCreated(new Date());
        p.setMobileSha256(mobileSha256);
        save(p);
        calcUserPoints(mobileSha256);
    }

    @Override
    public void grantSubject(GlSubjectRecord glSubjectRecord, Long companyId, String companyName, String mobileSha256) {
        GlPoints p = new GlPoints();
        p.setType(PointsType.SUBJECT.getCode());
        p.setTableName("gl_subject_record");
        p.setSourceId(glSubjectRecord.getId());
        p.setDescription("");
        p.setOrderId("");
        p.setId(null);
        p.setPoint(glSubjectRecord.getPoint().doubleValue());
        p.setCompanyId(companyId);
        p.setCompanyName(companyName);
        p.setCreated(new Date());
        p.setMobileSha256(mobileSha256);
        save(p);
        calcUserPoints(mobileSha256);
    }

    @Override
    public void rankGoodsPoints(GlGoods goods, String mobileSha256) {
        GlPoints p = new GlPoints();
        BeanUtil.copyProperties(goods, p,"type");
        p.setType(PointsType.EXCHANGE.getCode());
        p.setTableName("gl_goods");
        p.setSourceId(goods.getId());
        p.setDescription(goods.getName());
        p.setOrderId("");
        p.setId(null);
        p.setPoint(-0D);
        p.setCompanyId(goods.getCompanyId().longValue());
        p.setCompanyName(goods.getCompanyName());
        p.setCreated(new Date());
        p.setMobileSha256(mobileSha256);
        save(p);
        calcUserPoints(p.getMobileSha256());
    }

    @Override
    public void grantInvite(GlUserFriends userFriends, Double points, String nickname) {
        GlPoints p = new GlPoints();
        p.setType(PointsType.INVITE.getCode());
        p.setTableName("gl_user_friends");
        p.setSourceId(userFriends.getId());
        p.setDescription(String.format("邀请 '%s'",nickname));
        p.setOrderId("");
        p.setId(null);
        p.setPoint(points);
        p.setCompanyId(0L);
        p.setCompanyName("其它");
        p.setCreated(new Date());
        p.setMobileSha256(userFriends.getFriendsSha256());
        save(p);
        calcUserPoints(p.getMobileSha256());
    }

    @Override
    public void sendUserReadPoints(GlBusinessPointsLogDO glBusinessPointsLogDO, String mobile) {
        GlPoints p = new GlPoints();
        p.setType(PointsType.READ_DAY.getCode());

        GlCompany glCompany = glCompanyService.getById(121L);

        p.setTableName("gl_business_points_log");
        p.setSourceId(glBusinessPointsLogDO.getId());
        p.setDescription("用户阅读得积分");
        p.setId(null);
        p.setPoint(glBusinessPointsLogDO.getPoints().doubleValue());
        if(Objects.nonNull(glCompany)){
            p.setCompanyId(glCompany.getId());
            p.setCompanyName(glCompany.getName());
        }
        p.setCreated(new Date());
        p.setMobileSha256(mobile);
        save(p);
        calcUserPoints(mobile);
    }

    @Override
    public void grantBehaviorPoint(GlBehavior behavior, double rate) {
        GlPoints p = new GlPoints();
        BeanUtil.copyProperties(behavior, p);
        long id = behavior.getId() == null ? 0: behavior.getId();
        p.setType(PointsType.BEHAVIOR.getCode());
        GlCompany company = null;
        p.setDescription(behaviorMap.getString(String.valueOf(behavior.getBehaviorId())));
        company = glCompanyService.getCacheCompanyByTenantId(behavior.getTenantId().longValue());
        if(company == null){
            log.error("company empty tenantId:{}",behavior.getTenantId());
            return;
        }
        p.setTableName("gl_behavior");
        p.setSourceId(behavior.getId());
        p.setOrderId(behavior.getEventId());
        p.setId(null);
        p.setPoint(NumberUtil.round(NumberUtil.mul(behavior.getEmission().doubleValue(),rate),
                0, RoundingMode.UP).doubleValue());
        p.setCompanyId(company.getId());
        p.setCompanyName(company.getName());
        p.setCreated(new Date());
        save(p);
    }

    @Override
    public void grantQuestion(GlQuestionAnswerRecords glQuestionAnswerRecords, Long companyId, String companyName, String mobileSha256, Long id) {
        GlPoints p = new GlPoints();
        BeanUtil.copyProperties(glQuestionAnswerRecords, p);
        p.setType(PointsType.QUESTION.getCode());
        p.setTableName("gl_question_answer_records");
        p.setSourceId(glQuestionAnswerRecords.getId());
        p.setDescription("");
        p.setOrderId(id.toString());
        p.setId(null);
        p.setPoint(glQuestionAnswerRecords.getPoint().doubleValue());
        p.setCompanyId(companyId);
        p.setCompanyName(companyName);
        p.setCreated(new Date());
        p.setMobileSha256(mobileSha256);
        save(p);
        calcUserPoints(mobileSha256);
    }

    @Override
    public void grantTickoff(GlTickoff tickoff, Long companyId) {
        GlTickoffType tickoffType = glTickoffTypeService.getOne(new LambdaQueryWrapper<GlTickoffType>()
                .eq(GlTickoffType::getId,tickoff.getTypeId()));
        if(tickoffType ==null){
            throw new GicWxAppException("TickoffType Empty typeid:"+tickoff.getTypeId());
        }
        GlPoints p = new GlPoints();
        BeanUtil.copyProperties(tickoff, p);
        p.setType(PointsType.TICK_OFF.getCode());
        p.setTableName("gl_tickoff");
        p.setSourceId(tickoff.getId());
        p.setDescription("");
        p.setOrderId("");
        p.setId(null);
        p.setPoint(tickoffType.getPoint().doubleValue());
        GlCompany company = glCompanyService.getOne(new LambdaQueryWrapper<GlCompany>().eq(GlCompany::getId,companyId));
        if(company == null){
            throw new GicWxAppException("company Empty companyId:"+companyId);
        }
        p.setCompanyId(company.getId());
        p.setCompanyName(company.getName());
        p.setCreated(new Date());
        save(p);
        calcUserPoints(tickoff.getMobileSha256());
    }

    @Override
    public void grantUsrPoints(GlPoints p) {
        GlUser user = glUserService.getOne(new LambdaQueryWrapper<GlUser>().eq(GlUser::getMobileSha256,p.getMobileSha256()));
        if(user != null){
            user.setObtainTimes(user.getObtainTimes()+1);
            user.setPointTotal(NumberUtil.add(user.getPointTotal(),p.getPoint()).longValue());
            user.setPointRemain(NumberUtil.add(user.getPointRemain(),p.getPoint()).longValue());
            user.setUpdated(new Date());
            glUserService.update(new LambdaUpdateWrapper<GlUser>()
                    .set(GlUser::getObtainTimes,user.getObtainTimes())
                    .set(GlUser::getPointTotal,user.getPointTotal())
                    .set(GlUser::getPointRemain,user.getPointRemain())
                    .set(GlUser::getUpdated,user.getUpdated())
                    .eq(GlUser::getId,user.getId()));
        }
    }

    @Override
    public void grantUserPoints(Set<String> set) {
        if(CollectionUtil.isEmpty(set)){
            LambdaQueryWrapper<GlUser> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.select(GlUser::getMobileSha256);
            List<GlUser> glUsers = glUserService.list(lambdaQueryWrapper);
            set = glUsers.stream().map(GlUser::getMobileSha256).collect(Collectors.toSet());
        }
        log.info("重新计算用户积分 {}",set.size());
        set.forEach(this::calcUserPoints);
    }

    @Override
    public JSONObject getBehaviorMap() {
        return behaviorMap;
    }
}