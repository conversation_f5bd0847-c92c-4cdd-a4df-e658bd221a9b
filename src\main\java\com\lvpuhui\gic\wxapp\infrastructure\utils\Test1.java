package com.lvpuhui.gic.wxapp.infrastructure.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Test1 {
    private static final String CODE_FILE = "D:/code2.txt";
    private static final String PROCESS_LOG = "D:/process2.log";
    private static final String MATCHED_OUTPUT = "D:/matched_result.txt";
    private static final String UNMATCHED_OUTPUT = "D:/unmatched_result.txt";

    public static void main(String[] args) {
        try {
            // 第一步：解析code2.txt，建立mobileSha256到token的映射
            Map<String, String> tokenToMobileMap = parseCodeFile();

            // 第二步：处理process2.log文件
            processLogFiles(tokenToMobileMap);

            System.out.println("处理完成！");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static Map<String, String> parseCodeFile() throws IOException {
        Map<String, String> tokenToMobileMap = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();

        try (BufferedReader reader = new BufferedReader(new FileReader(CODE_FILE))) {
            String line;
            while ((line = reader.readLine()) != null) {
                try {
                    JsonNode rootNode = mapper.readTree(line);
                    JsonNode dataNode = rootNode.get("data");

                    if (dataNode != null) {
                        String mobileSha256 = dataNode.get("mobileSha256").asText();
                        String token = dataNode.get("token").asText();

                        tokenToMobileMap.put(token, mobileSha256);
                    }
                } catch (Exception e) {
                    System.err.println("解析行数据出错: " + line);
                    e.printStackTrace();
                }
            }
        }

        return tokenToMobileMap;
    }

    private static void processLogFiles(Map<String, String> tokenToMobileMap) throws IOException {
        List<String> matchedResults = new ArrayList<>();
        List<String> unmatchedResults = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(PROCESS_LOG))) {
            String token;
            while ((token = reader.readLine()) != null) {
                token = token.trim();
                if (token.isEmpty()) continue;

                String mobileSha256 = tokenToMobileMap.get(token);
                if (mobileSha256 != null) {
                    matchedResults.add(token + "," + mobileSha256);
                } else {
                    unmatchedResults.add(token);
                }
            }
        }

        // 写入匹配结果
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(MATCHED_OUTPUT))) {
            for (String result : matchedResults) {
                writer.write(result);
                writer.newLine();
            }
        }

        // 写入未匹配结果
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(UNMATCHED_OUTPUT))) {
            for (String result : unmatchedResults) {
                writer.write(result);
                writer.newLine();
            }
        }
    }
}
