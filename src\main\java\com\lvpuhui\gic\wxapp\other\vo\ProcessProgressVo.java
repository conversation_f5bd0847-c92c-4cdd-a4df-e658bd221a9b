package com.lvpuhui.gic.wxapp.other.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 处理进度VO
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ProcessProgressVo {

    /**
     * 状态：RUNNING, PAUSED, COMPLETED, FAILED
     */
    private String status;

    /**
     * 最后处理的源表ID
     */
    private Long lastProcessedId;

    /**
     * 已处理总数
     */
    private Long totalProcessed;

    /**
     * 已发放积分总数
     */
    private Long totalGrantedPoints;

    /**
     * 重复记录数
     */
    private Long duplicateCount;

    /**
     * 处理速度（条/秒）
     */
    private Double processSpeed;

    /**
     * 预计剩余时间（秒）
     */
    private Long estimatedRemainingTime;

    /**
     * 各分表处理情况
     */
    private Map<String, TableProgressInfo> tableProgress;

    /**
     * 队列积压情况
     */
    private Map<String, Integer> queueBacklog;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 最后更新时间
     */
    private Date lastUpdated;

    @Data
    @Accessors(chain = true)
    public static class TableProgressInfo {
        private String tableSuffix;
        private Long processedCount;
        private Long grantedPointsCount;
        private Long duplicateCount;
        private Date lastUpdated;
    }
}
