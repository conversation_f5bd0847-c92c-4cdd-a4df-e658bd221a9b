package com.lvpuhui.gic.wxapp.other.utils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehaviorTmp;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorTmpService;
import com.lvpuhui.gic.wxapp.infrastructure.sharding.DynamicTableNameHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 数据读取线程
 * <AUTHOR>
 */
@Slf4j
public class DataReaderThread implements Runnable {

    private static final int READ_BATCH_SIZE = 10000;
    
    private final GlBehaviorTmpService glBehaviorTmpService;
    private final TableQueueManager queueManager;
    private final ProcessBehaviorProgressManager progressManager;
    
    private final AtomicBoolean stopped = new AtomicBoolean(false);
    private volatile boolean paused = false;

    public DataReaderThread(GlBehaviorTmpService glBehaviorTmpService,
                           TableQueueManager queueManager,
                           ProcessBehaviorProgressManager progressManager) {
        this.glBehaviorTmpService = glBehaviorTmpService;
        this.queueManager = queueManager;
        this.progressManager = progressManager;
    }

    @Override
    public void run() {
        log.info("数据读取线程启动");
        
        try {
            Long lastId = progressManager.getLastProcessedId();
            
            while (!stopped.get()) {
                // 检查是否暂停
                if (progressManager.isPaused()) {
                    log.info("数据读取线程暂停中...");
                    Thread.sleep(1000);
                    continue;
                }
                
                // 读取一批数据
                List<GlBehaviorTmp> dataList = readBatch(lastId);
                
                if (dataList.isEmpty()) {
                    log.info("没有更多数据需要处理，数据读取线程结束");
                    break;
                }
                
                log.info("读取到 {} 条数据，最后ID: {}", dataList.size(), dataList.get(dataList.size() - 1).getId());
                
                // 分发数据到队列
                boolean success = queueManager.offerBatch(dataList);
                if (!success) {
                    log.error("数据分发失败，线程退出");
                    break;
                }
                
                // 更新最后处理的ID
                lastId = dataList.get(dataList.size() - 1).getId();
                progressManager.updateMainProgress(lastId);
                
                // 检查队列积压情况，如果积压过多则等待
                while (queueManager.getTotalQueueSize() > 50000 && !stopped.get()) {
                    log.info("队列积压过多，等待处理...");
                    Thread.sleep(1000);
                }
            }
            
        } catch (Exception e) {
            log.error("数据读取线程异常", e);
            progressManager.failProcess("数据读取线程异常: " + e.getMessage());
        }
        
        log.info("数据读取线程结束");
    }

    /**
     * 读取一批数据
     */
    private List<GlBehaviorTmp> readBatch(Long lastId) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        
        try {
            DynamicTableNameHolder.set("gl_behavior_tmp_copy1");
            
            LambdaQueryWrapper<GlBehaviorTmp> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.gt(GlBehaviorTmp::getId, lastId);
            queryWrapper.orderByAsc(GlBehaviorTmp::getId);
            queryWrapper.last("LIMIT " + READ_BATCH_SIZE);
            
            List<GlBehaviorTmp> result = glBehaviorTmpService.list(queryWrapper);
            
            stopWatch.stop();
            log.debug("读取 {} 条数据，耗时: {} 秒", result.size(), stopWatch.getTotalTimeSeconds());
            
            return result;
            
        } catch (Exception e) {
            log.error("读取数据异常", e);
            throw new RuntimeException("读取数据异常: " + e.getMessage());
        } finally {
            DynamicTableNameHolder.remove();
        }
    }

    /**
     * 停止线程
     */
    public void stop() {
        stopped.set(true);
        log.info("数据读取线程收到停止信号");
    }

    /**
     * 暂停线程
     */
    public void pause() {
        paused = true;
        log.info("数据读取线程暂停");
    }

    /**
     * 恢复线程
     */
    public void resume() {
        paused = false;
        log.info("数据读取线程恢复");
    }

    /**
     * 检查是否已停止
     */
    public boolean isStopped() {
        return stopped.get();
    }
}
