package com.lvpuhui.gic.wxapp.other.utils;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lvpuhui.gic.wxapp.carbonbook.dao.GlBehaviorDao;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehavior;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehaviorTmp;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorTmpService;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.infrastructure.sharding.DynamicTableNameHolder;
import com.lvpuhui.gic.wxapp.other.service.DataHandleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
public class ProcessBehaviorRunnable implements Runnable{

    private Boolean stop = false;

    private double rate;

    private GlPointsService glPointsService;

    private GlBehaviorTmpService glBehaviorTmpService;

    private GlBehaviorDao glBehaviorDao;

    private DataHandleService dataHandleService;

    public ProcessBehaviorRunnable(GlPointsService glPointsService, GlBehaviorTmpService glBehaviorTmpService, GlBehaviorDao glBehaviorDao, double rate,DataHandleService dataHandleService) {
        this.glPointsService = glPointsService;
        this.glBehaviorTmpService = glBehaviorTmpService;
        this.glBehaviorDao = glBehaviorDao;
        this.rate = rate;
        this.dataHandleService = dataHandleService;
    }

    public void stop(){
        stop = true;
    }

    @Override
    public void run() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        AtomicLong count = new AtomicLong(0);
        // 1.从gl_behavior_tmp中循环获取数据，每次记录一下最后的id，每次查询5000条数据，根据id正序排序
        int poolSize = Runtime.getRuntime().availableProcessors() * 2;
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                poolSize, // corePoolSize
                poolSize, // maximumPoolSize
                0L, TimeUnit.MILLISECONDS, // keepAliveTime, unit
                new LinkedBlockingQueue<>(100000), Executors.defaultThreadFactory(),new ThreadPoolExecutor.CallerRunsPolicy());
        try {
            Long lastId = 0L;
            while (true){
                if(stop){
                    log.error("while线程停止执行:{}",Thread.currentThread().getName());
                    break;
                }
                LambdaQueryWrapper<GlBehaviorTmp> glBehaviorTmpLambdaQueryWrapper = Wrappers.lambdaQuery();
                glBehaviorTmpLambdaQueryWrapper.gt(GlBehaviorTmp::getId,lastId);
//                glBehaviorTmpLambdaQueryWrapper.le(GlBehaviorTmp::getId,100000);
                glBehaviorTmpLambdaQueryWrapper.orderByAsc(GlBehaviorTmp::getId);
                glBehaviorTmpLambdaQueryWrapper.last("limit 10000");
                StopWatch stopWatch1 = new StopWatch();
                stopWatch1.start();
                List<GlBehaviorTmp> glBehaviorTmps = null;
                try {
                    DynamicTableNameHolder.set("gl_behavior_tmp_copy1");
                    glBehaviorTmps = glBehaviorTmpService.list(glBehaviorTmpLambdaQueryWrapper);
                }catch (Exception e){
                    log.error("查询gl_behavior_tmp异常:",e);
                }finally {
                    DynamicTableNameHolder.remove();
                }
//                List<GlBehaviorTmp> glBehaviorTmps = glBehaviorTmpService.list(glBehaviorTmpLambdaQueryWrapper);
                if(CollUtil.isEmpty(glBehaviorTmps)){
                    break;
                }
                stopWatch1.stop();
                log.info("最后id:{},查询到{}条数据，耗时:{}秒",lastId,glBehaviorTmps.size(),stopWatch1.getTotalTimeSeconds());
                lastId = CollUtil.getLast(glBehaviorTmps).getId();
                CountDownLatch countDownLatch = new CountDownLatch(glBehaviorTmps.size());
                log.info("已处理数据:{}条",count.get());
                glBehaviorTmps.forEach(glBehaviorTmp -> {
                    if(stop){
                        log.error("while for线程停止执行:{}",Thread.currentThread().getName());
                        return;
                    }
                    executor.execute(() -> {
                        countDownLatch.countDown();
                        if(stop){
                            log.error("executor线程停止执行:{}",Thread.currentThread().getName());
                            return;
                        }
                        count.incrementAndGet();
                        GlBehavior glBehavior = new GlBehavior();
                        glBehavior.setId(IdWorker.getId());
                        glBehavior.setEventId(glBehaviorTmp.getEventId());
                        glBehavior.setRegion(glBehaviorTmp.getRegion());
                        glBehavior.setTenantId(glBehaviorTmp.getTenantId().longValue());
                        glBehavior.setAppId(glBehaviorTmp.getAppId());
                        glBehavior.setScenarioId(glBehaviorTmp.getScenarioId());
                        glBehavior.setBehaviorId(glBehaviorTmp.getActId());
                        glBehavior.setMobileSha256(glBehaviorTmp.getMobileSha256());
                        glBehavior.setDate(glBehaviorTmp.getDate());
                        glBehavior.setEmission(glBehaviorTmp.getEmission());
                        glBehavior.setCreated(glBehaviorTmp.getCreated());
                        glBehavior.setType(0);
                        glBehavior.setDeleted(0);

//                        BeanUtil.copyProperties(glBehaviorTmp, glBehavior);
//                        glBehavior.setId(IdWorker.getId());
//                        glBehavior.setBehaviorId(glBehaviorTmp.getActId());
//                        glBehavior.setType(0);
//                        glBehavior.setDeleted(0);
//                        glBehavior.setCreated(new Date());
                        try {
                            glBehaviorDao.insert(glBehavior);
                        } catch (Exception e){
                            if(e instanceof DuplicateKeyException){
                                log.error("tenantId:{} and eventId:{}DuplicateKey",glBehavior.getTenantId(),glBehavior.getEventId());
                            }else {
                                log.error("save behavior error:",e);
                            }
                            return;
                        }
                        try {
                            glPointsService.grantBehaviorPoint(glBehavior,rate);
                        }catch (Exception e){
                            log.error("grant behavior point error:",e);
                        }
                    });
                });
                countDownLatch.await();
            }
            glPointsService.grantUserPoints(null);
        }catch (Exception e){
            log.error("线程池异常:",e);
        }finally {
            executor.shutdown();
        }
        stopWatch.stop();
        log.info("处理绿色行为完成，耗时:{}秒",stopWatch.getTotalTimeSeconds());
//        dataHandleService.callStatisticsMethod();
    }
}
