package com.lvpuhui.gic.wxapp.other.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 绿色行为分表处理进度表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("process_behavior_table_progress")
public class ProcessBehaviorTableProgress {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 分表后缀
     */
    private String tableSuffix;

    /**
     * 该分表已处理数量
     */
    private Long processedCount;

    /**
     * 该分表已发放积分数量
     */
    private Long grantedPointsCount;

    /**
     * 该分表重复记录数量
     */
    private Long duplicateCount;

    /**
     * 最后更新时间
     */
    private Date lastUpdated;
}
