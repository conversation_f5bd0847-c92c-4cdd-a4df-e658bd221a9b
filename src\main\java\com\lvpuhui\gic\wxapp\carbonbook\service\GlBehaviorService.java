package com.lvpuhui.gic.wxapp.carbonbook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lvpuhui.gic.wxapp.carbonbook.dto.TmpDateBetweenDto;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehavior;
import com.lvpuhui.gic.wxapp.other.entity.GlConsumption;
import com.lvpuhui.gic.wxapp.carbonbook.dto.SceneDetailDto;
import com.lvpuhui.gic.wxapp.carbonbook.dto.SceneVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * 绿色行为记录表 (来源大数据增量)(GlBehavior)表服务接口
 *
 * <AUTHOR>
 * @since 2022-05-06 13:45:10
 */
public interface GlBehaviorService extends IService<GlBehavior> {

    /**
     * 处理绿色行为增量数据
     * @param date
     * @return
     */
    int handleBehaviorV2(String date);

    /**
     * 处理绿色行为发放积分
     * @param date
     * @return
     */
    long handlePointsV2(String date);


    /**
     * 处理绿色消费生成减排量和对应减排量积分
     * @param consumptions
     */
    void  handleConsumption(List<GlConsumption> consumptions);

    /**
     * 计算减排量
     * @param mobileSha256
     */
    void calcPoints(String mobileSha256);

    /**
     * 获取减排量根据场景分组
     * @param mobileSha256 手机号
     * @return
     */
    List<SceneVo> getBehaviorGroupByScenarios(String mobileSha256);

    /**
     * 查询减排量详情
     * @param mobileSha256 手机号
     * @param offset 偏移量
     * @param size 每页大小
     */
    List<SceneDetailDto> getBehaviorDetailGroupByScenarios(String mobileSha256, int offset, Integer size, String ids);

    /**
     * 查询减排量详情总数
     * @param mobileSha256 手机号
     * @param ids 场景ID
     */
    Integer getBehaviorDetailCount(String mobileSha256, String ids);

    /**
     * 根据手机号获取全部减排量
     * @param mobileSha256 手机号
     * @return 减排量
     */
    BigDecimal getSumEmissionByMobileSha256(String mobileSha256);

    /**
     * 获取开始时间和结束时间
     */
    TmpDateBetweenDto getTmpDateBetween();

    /**
     * 处理数据
     */
    void processBehavior();

    void handlerMobilePoints();

    void handlerMobilePointsDate();
}