package com.lvpuhui.gic.wxapp.other.utils;

import com.google.common.collect.Lists;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehaviorTmp;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 分表队列管理器
 * <AUTHOR>
 */
@Slf4j
public class TableQueueManager {

    private static final int QUEUE_CAPACITY = 1000;
    private static final String[] HEX_ARRAY = {"0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"};
    
    /**
     * 256个分表队列
     */
    private final Map<String, ConcurrentLinkedQueue<GlBehaviorTmp>> tableQueues;
    
    /**
     * 队列大小计数器
     */
    private final Map<String, AtomicInteger> queueSizes;

    public TableQueueManager() {
        this.tableQueues = new HashMap<>(256);
        this.queueSizes = new HashMap<>(256);
        
        // 初始化256个队列
        for (String i : HEX_ARRAY) {
            for (String j : HEX_ARRAY) {
                String suffix = i + j;
                tableQueues.put(suffix, new ConcurrentLinkedQueue<>());
                queueSizes.put(suffix, new AtomicInteger(0));
            }
        }
    }

    /**
     * 根据mobile_sha256前缀获取队列后缀
     */
    public String getTableSuffix(String mobileSha256) {
        if (mobileSha256 == null || mobileSha256.length() < 2) {
            throw new IllegalArgumentException("mobile_sha256不能为空");
        }
        return mobileSha256.substring(0, 2).toLowerCase();
    }

    /**
     * 添加数据到对应队列
     * @param data 数据
     * @return 是否添加成功
     */
    public boolean offer(GlBehaviorTmp data) {
        String suffix = getTableSuffix(data.getMobileSha256());
        ConcurrentLinkedQueue<GlBehaviorTmp> queue = tableQueues.get(suffix);
        AtomicInteger size = queueSizes.get(suffix);
        
        if (queue == null || size == null) {
            log.error("队列不存在，suffix: {}", suffix);
            return false;
        }
        
        // 检查队列容量
        if (size.get() >= QUEUE_CAPACITY) {
            return false;
        }
        
        boolean success = queue.offer(data);
        if (success) {
            size.incrementAndGet();
        }
        return success;
    }

    /**
     * 批量添加数据到对应队列
     * @param dataList 数据列表
     * @return 添加失败的数据列表
     */
    public boolean offerBatch(List<GlBehaviorTmp> dataList) {
        for (GlBehaviorTmp data : dataList) {
            while (!offer(data)) {
                // 如果队列满了，等待一段时间再试
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 从指定队列批量取出数据
     * @param suffix 分表后缀
     * @param batchSize 批次大小
     * @return 数据列表
     */
    public List<GlBehaviorTmp> pollBatch(String suffix, int batchSize) {
        ConcurrentLinkedQueue<GlBehaviorTmp> queue = tableQueues.get(suffix);
        AtomicInteger size = queueSizes.get(suffix);
        
        if (queue == null || size == null) {
            return Lists.newArrayList();
        }
        
        List<GlBehaviorTmp> result = new java.util.ArrayList<>();
        for (int i = 0; i < batchSize && !queue.isEmpty(); i++) {
            GlBehaviorTmp data = queue.poll();
            if (data != null) {
                result.add(data);
                size.decrementAndGet();
            }
        }
        
        return result;
    }

    /**
     * 获取指定队列的大小
     */
    public int getQueueSize(String suffix) {
        AtomicInteger size = queueSizes.get(suffix);
        return size != null ? size.get() : 0;
    }

    /**
     * 获取所有队列的大小信息
     */
    public Map<String, Integer> getAllQueueSizes() {
        Map<String, Integer> result = new HashMap<>();
        for (Map.Entry<String, AtomicInteger> entry : queueSizes.entrySet()) {
            result.put(entry.getKey(), entry.getValue().get());
        }
        return result;
    }

    /**
     * 获取总的队列大小
     */
    public int getTotalQueueSize() {
        return queueSizes.values().stream().mapToInt(AtomicInteger::get).sum();
    }

    /**
     * 检查是否所有队列都为空
     */
    public boolean isEmpty() {
        return queueSizes.values().stream().allMatch(size -> size.get() == 0);
    }

    /**
     * 清空所有队列
     */
    public void clear() {
        tableQueues.values().forEach(ConcurrentLinkedQueue::clear);
        queueSizes.values().forEach(size -> size.set(0));
    }
}
