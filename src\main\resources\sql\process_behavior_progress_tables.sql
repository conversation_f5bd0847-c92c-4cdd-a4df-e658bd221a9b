-- 绿色行为处理进度表
CREATE TABLE IF NOT EXISTS `process_behavior_progress` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `last_processed_id` BIGINT NOT NULL DEFAULT 0 COMMENT '最后处理的源表ID',
    `total_processed` BIGINT NOT NULL DEFAULT 0 COMMENT '已处理总数',
    `total_granted_points` BIGINT NOT NULL DEFAULT 0 COMMENT '已发放积分总数',
    `duplicate_count` BIGINT NOT NULL DEFAULT 0 COMMENT '重复记录数',
    `status` VARCHAR(20) NOT NULL DEFAULT 'NOT_STARTED' COMMENT '状态：NOT_STARTED, RUNNING, PAUSED, COMPLETED, FAILED',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='绿色行为处理进度表';

-- 绿色行为分表处理进度表
CREATE TABLE IF NOT EXISTS `process_behavior_table_progress` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `table_suffix` VARCHAR(2) NOT NULL COMMENT '分表后缀',
    `processed_count` BIGINT NOT NULL DEFAULT 0 COMMENT '该分表已处理数量',
    `granted_points_count` BIGINT NOT NULL DEFAULT 0 COMMENT '该分表已发放积分数量',
    `duplicate_count` BIGINT NOT NULL DEFAULT 0 COMMENT '该分表重复记录数量',
    `last_updated` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_table_suffix` (`table_suffix`),
    INDEX `idx_last_updated` (`last_updated`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='绿色行为分表处理进度表';
