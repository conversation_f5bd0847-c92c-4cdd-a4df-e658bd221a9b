package com.lvpuhui.gic.wxapp.other.service;

import com.lvpuhui.gic.wxapp.other.vo.ProcessProgressVo;

/**
 * 绿色行为处理进度服务接口
 * <AUTHOR>
 */
public interface ProcessBehaviorProgressService {

    /**
     * 启动优化版处理器
     */
    void startOptimizedProcessor();

    /**
     * 获取当前处理进度
     */
    ProcessProgressVo getCurrentProgress();

    /**
     * 暂停处理
     */
    void pauseProcess();

    /**
     * 恢复处理
     */
    void resumeProcess();

    /**
     * 停止处理
     */
    void stopProcess();

    /**
     * 检查是否正在运行
     */
    boolean isRunning();
}
