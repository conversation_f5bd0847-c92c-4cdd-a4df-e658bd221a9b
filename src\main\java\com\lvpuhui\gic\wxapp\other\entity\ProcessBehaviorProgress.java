package com.lvpuhui.gic.wxapp.other.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 绿色行为处理进度表
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("process_behavior_progress")
public class ProcessBehaviorProgress {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 最后处理的源表ID
     */
    private Long lastProcessedId;

    /**
     * 已处理总数
     */
    private Long totalProcessed;

    /**
     * 已发放积分总数
     */
    private Long totalGrantedPoints;

    /**
     * 重复记录数
     */
    private Long duplicateCount;

    /**
     * 状态：RUNNING, PAUSED, COMPLETED, FAILED
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;
}
