package com.lvpuhui.gic.wxapp.infrastructure.sharding;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.baomidou.mybatisplus.extension.plugins.inner.DynamicTableNameInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
@Configuration
public class MybatisPlusConfig {


    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        DynamicTableNameInnerInterceptor dynamicTableNameInnerInterceptor = new DynamicTableNameInnerInterceptor();
        HashMap<String, TableNameHandler> map = new HashMap<String, TableNameHandler>(2) {{
            put("gl_behavior", (sql, tableName) -> {
                String tb = DynamicTableNameHolder.get();
                return StrUtil.isNotEmpty(tb) ? tb : tableName;
            });
            put("gl_points", (sql, tableName) -> {
                String tb = DynamicTableNameHolder.get();
                return StrUtil.isNotEmpty(tb) ? tb : tableName;
            });
            put("gl_behavior_tmp", (sql, tableName) -> {
                String tb = DynamicTableNameHolder.get();
                return StrUtil.isNotEmpty(tb) ? tb : tableName;
            });
        }};
        dynamicTableNameInnerInterceptor.setTableNameHandlerMap(map);
        interceptor.addInnerInterceptor(dynamicTableNameInnerInterceptor);
        return interceptor;
    }
}
