package com.lvpuhui.gic.wxapp.carbonbook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RuntimeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Entity;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.lvpuhui.gic.wxapp.base.entity.GlAppletConfig;
import com.lvpuhui.gic.wxapp.base.service.GlAppletConfigService;
import com.lvpuhui.gic.wxapp.carbonbook.dao.GlBehaviorDao;
import com.lvpuhui.gic.wxapp.carbonbook.dao.GlBehaviorTmpDao;
import com.lvpuhui.gic.wxapp.carbonbook.dto.SceneDetailDto;
import com.lvpuhui.gic.wxapp.carbonbook.dto.SceneVo;
import com.lvpuhui.gic.wxapp.carbonbook.dto.TmpDateBetweenDto;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehavior;
import com.lvpuhui.gic.wxapp.carbonbook.entity.GlBehaviorTmp;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorIdService;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorService;
import com.lvpuhui.gic.wxapp.carbonbook.service.GlBehaviorTmpService;
import com.lvpuhui.gic.wxapp.homepage.entity.GlCompany;
import com.lvpuhui.gic.wxapp.homepage.enums.PointsType;
import com.lvpuhui.gic.wxapp.homepage.service.GlCompanyService;
import com.lvpuhui.gic.wxapp.homepage.service.GlPointsService;
import com.lvpuhui.gic.wxapp.infrastructure.constant.Global;
import com.lvpuhui.gic.wxapp.infrastructure.sharding.DynamicTableNameHolder;
import com.lvpuhui.gic.wxapp.my.entity.GlUser;
import com.lvpuhui.gic.wxapp.my.service.GlUserService;
import com.lvpuhui.gic.wxapp.other.entity.GlConsumption;
import com.lvpuhui.gic.wxapp.other.service.DataHandleService;
import com.lvpuhui.gic.wxapp.other.utils.ProcessBehaviorRunnable;
import com.lvpuhui.gic.wxapp.other.utils.SimilarityHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 绿色行为记录表 (来源大数据增量)(GlBehavior)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-06 13:45:10
 */
@Service("glBehaviorService")
@Slf4j
public class GlBehaviorServiceImpl extends ServiceImpl<GlBehaviorDao, GlBehavior> implements GlBehaviorService {
    @Resource
    GlBehaviorIdService glBehaviorIdService;
    @Resource
    GlPointsService glPointsService;
    @Resource
    GlAppletConfigService glAppletConfigService;
    @Resource
    GlBehaviorTmpService glBehaviorTmpService;
    @Resource
    GlCompanyService glCompanyService;
    @Resource
    GlUserService glUserService;

    @Resource
    DataHandleService dataHandleService;

    @Resource
    private GlBehaviorTmpDao glBehaviorTmpDao;
    final float blockingCoefficient = 0f;
    final String [] array = new String[]{"0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"};
    final String [] categoryArray  = new String[]{"空调","冰箱","洗衣机","热水机","电视机"};
    final int batchCount = 1000;

    ProcessBehaviorRunnable lastProcessBehaviorRunnable = null;


    @Override
    public int handleBehaviorV2(String date) {
        String start = null;
        String end = null;
        if(StrUtil.isNotBlank(date)){
            String [] array = date.split(",");
            start = array[0];
            end = array[1];
        }
        int poolSize = (int) (RuntimeUtil.getProcessorCount() / (1 - blockingCoefficient));
        ThreadPoolExecutor executorService = new ThreadPoolExecutor(poolSize, poolSize,
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1024),
                new ThreadFactoryBuilder().setNameFormat("handleBehaviorV2-t-%d").build(),new ThreadPoolExecutor.CallerRunsPolicy());
        AtomicLong remain = new AtomicLong(1);
        AtomicInteger atomicInteger = new AtomicInteger(0);
        try {
            for(int i=0;i<array.length;i++){
                for(int j =0;j<array.length;j++){
                    String key = String.format("%s%s",array[i],array[j]);
                        int total = StrUtil.isBlank(date) ?
                                glBehaviorTmpService.count(new LambdaQueryWrapper<GlBehaviorTmp>()
                                .likeRight(GlBehaviorTmp::getMobileSha256,key)) :
                                glBehaviorTmpService.count(new LambdaQueryWrapper<GlBehaviorTmp>()
                                        .likeRight(GlBehaviorTmp::getMobileSha256,key)
                                        .between(GlBehaviorTmp::getDate, start, end));
                        if(total ==0){
                            continue;
                        }
                        atomicInteger.addAndGet(total);
                        int ceil = (int)Math.ceil((double)total/batchCount);
                        for(int c=0;c<ceil;c++) {
                            LambdaQueryWrapper<GlBehaviorTmp> lambdaQueryWrapper = new LambdaQueryWrapper();
                            lambdaQueryWrapper.likeRight(GlBehaviorTmp::getMobileSha256,key);
                            if (StrUtil.isNotBlank(date)) {
                                lambdaQueryWrapper.between(GlBehaviorTmp::getDate, start, end);
                            }
                            lambdaQueryWrapper.orderByAsc(GlBehaviorTmp::getDate, GlBehaviorTmp::getEventId);
                            lambdaQueryWrapper.last(String.format(" limit %s, %s", batchCount * c, batchCount));
                            List<GlBehaviorTmp> behaviors = glBehaviorTmpService.list(lambdaQueryWrapper);
                            int size = behaviors.size();
                            log.info("handleBehaviorV2 key:{} size: {} execute {}", key, size, remain.get());
                            try {
                                behaviors.forEach(b-> executorService.execute(()->{
                                    GlBehavior glBehavior = new GlBehavior();
                                    BeanUtil.copyProperties(b, glBehavior);
                                    glBehavior.setId(IdWorker.getId());
                                    glBehavior.setBehaviorId(b.getActId());
                                    glBehavior.setType(0);
                                    glBehavior.setDeleted(0);
                                    glBehavior.setCreated(new Date());
                                    try {
                                        save(glBehavior);
                                    }catch (DuplicateKeyException duplicateKeyException){
                                        log.error("此减排量已存在:{},{}",glBehavior.getEventId(),glBehavior.getTenantId());
                                    }catch (Exception e){
                                        if(e instanceof SQLIntegrityConstraintViolationException){
                                            log.error("此减排量已存在:{},{}",glBehavior.getEventId(),glBehavior.getTenantId());
                                        }else {
                                            log.error("存储减排量异常:{}",e);
                                        }
                                    }
                                }));
                            }finally {
                                remain.incrementAndGet();
                            }
                        }
                }
            }
        } finally {
            executorService.shutdown();
        }
        return atomicInteger.get();
    }

    @Override
    public long handlePointsV2(String date) {
        String start = null;
        String end = null;
        if(StrUtil.isNotBlank(date)){
            String [] array = date.split(",");
            start = array[0];
            end = array[1];
        }
        ThreadPoolExecutor executorService = new ThreadPoolExecutor(
                6, // corePoolSize
                6, // maximumPoolSize
                0L, TimeUnit.MILLISECONDS, // keepAliveTime, unit
                new LinkedBlockingQueue<>(50000), Executors.defaultThreadFactory(),new ThreadPoolExecutor.CallerRunsPolicy());
        long result = 0;
        AtomicLong remain = new AtomicLong(1);
        double rate = glAppletConfigService.getBehaviorToPoints();
        try {
            for(int i=0;i<array.length;i++) {
                for (int j = 0; j < array.length; j++) {
                    String key = String.format("%s%s", array[i], array[j]);
                    try {
                        DynamicTableNameHolder.set(String.format("gl_behavior_%s",key));
                        int total = StrUtil.isBlank(date) ?
                                count(new LambdaQueryWrapper<>()) :
                                count(new LambdaQueryWrapper<GlBehavior>()
                                        .between(GlBehavior::getDate, start, end));
                        if (total == 0) {
                            continue;
                        }
                        result += total;
                        int ceil = (int)Math.ceil((double)total/batchCount);
                        for(int c=0;c<ceil;c++) {
                            try {
                                LambdaQueryWrapper<GlBehavior> lambdaQueryWrapper = new LambdaQueryWrapper();
                                if (StrUtil.isNotBlank(date)) {
                                    lambdaQueryWrapper.between(GlBehavior::getDate, start, end);
                                }
                                lambdaQueryWrapper.orderByAsc(GlBehavior::getDate, GlBehavior::getEventId);
                                lambdaQueryWrapper.last(String.format(" limit %s, %s", batchCount * c, batchCount));
                                List<GlBehavior> behaviors = list(lambdaQueryWrapper);
                                int size = behaviors.size();
                                log.info("handlePointsV2 key:{} size: {} execute {}", key, size, remain.get());
                                CountDownLatch latch = new CountDownLatch(size);
                                behaviors.stream().forEach(b->{
                                    executorService.execute(()->{
                                        try {
                                            glPointsService.grantBehaviorPoints2(b,rate);
                                        } finally {
                                            latch.countDown();
                                        }
                                    });
                                });
                                try {
                                    latch.await();
                                } catch (InterruptedException e) {
                                    log.error(e.getMessage(),e);
                                }
                            } finally {
                                remain.incrementAndGet();
                            }
                        }
                    } finally {
                        DynamicTableNameHolder.remove();
                    }
                }
            }
        }catch (Exception e){
            log.error("handlePointsV2 error",e);
        }finally {
            glPointsService.grantUserPoints(null);
            executorService.shutdown();
        }
        return result;
    }


    String getCos(String category,String [] array){
        String result = null;
        float f = 0f;
        for (String str:array){
            float fa = SimilarityHelper.cos(category, str);
            if(fa> f){
                f =fa;
                result = str;
            }
        }
        if(f>0.27f){
            return result;
        }
        return null;
    }
     Double getEmission(JSONObject jsonObject, GlConsumption c){
        // 一级品类
        String category = c.getCategory().length()> 3 ? c.getCategory().substring(0,3):c.getCategory();
        // 二级品类
        String subCategory = c.getSubCategory();
        // 能耗等级
        String energyLevel = c.getEnergyLevel();
        // 如果二级品类为空，按照商品名称匹配相似度处理
        if(StrUtil.isBlank(subCategory)){
            String newCategory = getCos(c.getProductName(),categoryArray);
            if(StrUtil.isBlank(newCategory)){
                return null;
            }
             // "空调","冰箱","洗衣机","热水机","电视机"
            if (newCategory.equals("空调")) {
                return 100D;
            }else if(newCategory.equals("冰箱")){
                return 79D;
            }else if(newCategory.equals("洗衣机")){
                return 22D;
            }else if(newCategory.equals("热水机")){
                return 80D;
            }else if(newCategory.equals("电视机")){
                return 70D;
            }else {
                return null;
            }
        }
        String level = String.format("level%s",energyLevel);
        JSONObject object = category.equals("彩电")?jsonObject.getJSONObject("电视机"):jsonObject.getJSONObject(category);
        if(object ==null){
            return null;
        }
        JSONObject emissionObject = object.getJSONObject(subCategory);
        if(emissionObject!=null){
            return Double.parseDouble(emissionObject.get(level).toString());
        }
        String pName = c.getProductName();
        if(category.equals("热水器")){
            if(pName.contains("燃气热水器")){
                emissionObject = object.getJSONObject("燃气");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):180D;
            }else {
                emissionObject = object.getJSONObject("电");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):80D;
            }
        }else if(category.equals("洗衣机")){
            if(pName.contains("波轮")){
                emissionObject = object.getJSONObject(String.format("%s%s","波轮",subCategory));
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):25D;
            }else {
                emissionObject = object.getJSONObject(String.format("%s%s","滚筒",subCategory));
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):22D;
            }
        }else if(category.equals("空调")){
            if(subCategory.equals("1匹") || subCategory.equals("1.5匹")){
                emissionObject = object.getJSONObject("1.5p级以下");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):110;
            }else if(subCategory.equals("2匹")){
                emissionObject = object.getJSONObject("2p");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):100;
            }else if(subCategory.equals("3匹")){
                emissionObject = object.getJSONObject("3p");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):0;
            }else {
                return 0D;
            }
        }else if(category.equals("冰箱")){
            double number = getNumber(subCategory);
            if(number >= 200 && number <=400){
                emissionObject = object.getJSONObject("200-400");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):79;
            }else if(number > 400 && number <=500){
                emissionObject = object.getJSONObject("400-500");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):91;
            }else if(number > 500 && number <=600){
                emissionObject = object.getJSONObject("500-600");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):103;
            }else if(number>600){
                emissionObject = object.getJSONObject("600升以上");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):139;
            }else {
                return 79D;
            }
        }else if(category.equals("彩电") || category.equals("电视机")){
            double number = getNumber(subCategory);
            if(number <= 55){
                emissionObject = object.getJSONObject("55寸");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):70;
            }else if(number > 55 && number <= 65){
                emissionObject = object.getJSONObject("65寸");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):90;
            }else if(number > 65 && number <= 75){
                emissionObject = object.getJSONObject("75寸");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):145;
            }else if(number > 75 && number <= 85){
                emissionObject = object.getJSONObject("85寸");
                return emissionObject !=null?Double.parseDouble(emissionObject.get(level).toString()):160;
            }else {
                return 70D;
            }
        }
        return null;
    }

     double getNumber(String str){
        StringBuffer code = new StringBuffer();
        String regEx="[0-9]";
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(str);
        while(matcher.find()){
            String group = matcher.group();
            code.append(group);
        }
        return Double.parseDouble(code.toString());
    }

    @Override
    public void handleConsumption(List<GlConsumption> consumptions){
        // get config and formatter
        GlAppletConfig config = glAppletConfigService.getOne(new LambdaQueryWrapper<GlAppletConfig>()
                .eq(GlAppletConfig::getParamKey,Global.CONSUMPTION_EMISSION_REDUCTION)
                .last(" LIMIT 1"));
        JSONObject jsonObject = JSON.parseObject(config.getParamValue());
        List<GlBehavior> behaviors = consumptions.stream().map(c->{
            Double emission = getEmission(jsonObject,c);
            if(emission !=null){
                GlCompany company = glCompanyService.getById(c.getCompanyId());
                GlBehavior behavior = new GlBehavior();
                behavior.setEventId(c.getId()+"");
                behavior.setId(IdWorker.getId());
                behavior.setTenantId(company !=null ? company.getId() :0);
                behavior.setAppId(0);
                behavior.setScenarioId(0);
                behavior.setBehaviorId(0);
                behavior.setType(1);
                behavior.setCreated(new Date());
                behavior.setDate(c.getSoldTime());
                behavior.setEmission(NumberUtil.round(NumberUtil.div(NumberUtil.mul(emission.doubleValue(),1000),12),2).doubleValue());
                behavior.setRegion("110000");
                behavior.setMobileSha256(c.getMobileSha256());
                behavior.setDeleted(0);
                return behavior;
            }
            return null;
        }).filter(b-> b!=null).collect(Collectors.toList());
        log.info("处理绿色行为 -> {}",behaviors.size());
        double rate = glAppletConfigService.getBehaviorToPoints();
        behaviors.stream().forEach(b-> log.info("save {}", save(b)));
        behaviors.stream().forEach(b-> glPointsService.grantBehaviorPoints(b,rate, PointsType.LV_BEHAVIOR));
    }

    @Override
    public void calcPoints(String mobileSha256) {
        if(StrUtil.isNotBlank(mobileSha256)){
            glPointsService.calcUserPoints(mobileSha256);
            return;
        }
        List<GlUser> glUsers = glUserService.list(new LambdaQueryWrapper<GlUser>());
        glUsers.stream().forEach(u->{
            try {
                glPointsService.calcUserPoints(u.getMobileSha256());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
            }
        });

    }


    GlBehavior buildBehavior(Entity e){
        return new GlBehavior().setEventId(String.format("%s%s",e.getStr("event_id"),e.getLong("id_rank")))
                .setBehaviorId(e.getInt("act_id"))
                .setAppId(e.getInt("app_id"))
                .setEmission(e.getDouble("emission"))
                .setRegion(e.getStr("region"))
                .setDate(e.getTimestamp("date_"))
                .setMobileSha256(e.getStr("mobile_sha256"))
                .setTenantId(e.getLong("tenant_id"))
                .setScenarioId(e.getInt("scenario_id"));

    }

    @Override
    public List<SceneVo> getBehaviorGroupByScenarios(String mobileSha256) {
        return baseMapper.getBehaviorGroupByScenarios(mobileSha256);
    }

    @Override
    public List<SceneDetailDto> getBehaviorDetailGroupByScenarios(String mobileSha256, int offset, Integer size, String ids) {
        return baseMapper.getBehaviorDetailGroupByScenarios(mobileSha256,offset,size,ids);
    }

    @Override
    public Integer getBehaviorDetailCount(String mobileSha256, String ids) {
        return baseMapper.getBehaviorDetailCount(mobileSha256,ids);
    }

    @Override
    public BigDecimal getSumEmissionByMobileSha256(String mobileSha256) {
        return baseMapper.getSumEmissionByMobileSha256(mobileSha256);
    }

    @Override
    public TmpDateBetweenDto getTmpDateBetween(){
        return glBehaviorTmpDao.getTmpDateBetween();
    }

    @Override
    public void processBehavior() {
        if(Objects.nonNull(lastProcessBehaviorRunnable)){
            lastProcessBehaviorRunnable.stop();
        }
        double rate = glAppletConfigService.getBehaviorToPoints();
        ProcessBehaviorRunnable processBehaviorRunnable = new ProcessBehaviorRunnable(glPointsService,glBehaviorTmpService,baseMapper,rate,dataHandleService);
        Thread thread = new Thread(processBehaviorRunnable);
        lastProcessBehaviorRunnable = processBehaviorRunnable;
        thread.start();
    }

    @Override
    public void handlerMobilePoints() {
        LambdaQueryWrapper<GlUser> glUserLambdaQueryWrapper = Wrappers.lambdaQuery();
        glUserLambdaQueryWrapper.select(GlUser::getMobileSha256);
        glUserLambdaQueryWrapper.gt(GlUser::getId, 26080);
        List<GlUser> glUsers = glUserService.list(glUserLambdaQueryWrapper);

        List<String> jumpKeys = Lists.newArrayList("0","1","2","3","4","5","6","7","8","9","a","b");

        double rate = glAppletConfigService.getBehaviorToPoints();
        for (GlUser glUser : glUsers) {
            String mobileSha256 = glUser.getMobileSha256();
            String key = mobileSha256.substring(0,2).toLowerCase();
            String key1 = mobileSha256.substring(0,1).toLowerCase();
            log.info("处理手机号:{},key:{},key1:{}",mobileSha256, key,key1);
            if(jumpKeys.contains(key1)){
                log.info("跳过:{}",mobileSha256);
                continue;
            }
            try {
                DynamicTableNameHolder.set(String.format("gl_behavior_%s", key));
                LambdaQueryWrapper<GlBehavior> glBehaviorLambdaQueryWrapper = Wrappers.lambdaQuery();
                glBehaviorLambdaQueryWrapper.eq(GlBehavior::getMobileSha256, mobileSha256);
                List<GlBehavior> glBehaviors = baseMapper.selectList(glBehaviorLambdaQueryWrapper);
                for (GlBehavior glBehavior : glBehaviors) {
                    try {
                        DynamicTableNameHolder.set(String.format("gl_points_%s", key));
                        glPointsService.grantBehaviorPoints2(glBehavior,rate);
                    }catch (Exception e){
                        log.error("异常1:",e);
                    }finally {
                        DynamicTableNameHolder.remove();
                    }
                }
            }catch (Exception e){
                log.error("异常:",e);
            }finally {
                DynamicTableNameHolder.remove();
            }
        }
    }

    @Override
    public void handlerMobilePointsDate() {
        LambdaQueryWrapper<GlUser> glUserLambdaQueryWrapper = Wrappers.lambdaQuery();
        glUserLambdaQueryWrapper.select(GlUser::getMobileSha256);
//        glUserLambdaQueryWrapper.notIn(GlUser::getMobileSha256, Lists.newArrayList("ece59c8c67ea9cd39f9f14ce0f709d7c0da68abeb0ebfbf954158a14301ab77c","f91ef18fa837155250a8fb81b6debd55d260e4d06a7a68d460f0b2afb3569d0e"));
        List<GlUser> glUsers = glUserService.list(glUserLambdaQueryWrapper);
        for (GlUser glUser : glUsers) {
            String mobileSha256 = glUser.getMobileSha256();
            String key = mobileSha256.substring(0,2).toLowerCase();
            try {
                DynamicTableNameHolder.set(String.format("gl_behavior_%s", key));
                LambdaQueryWrapper<GlBehavior> glBehaviorLambdaQueryWrapper = Wrappers.lambdaQuery();
                glBehaviorLambdaQueryWrapper.eq(GlBehavior::getMobileSha256, mobileSha256);
                glBehaviorLambdaQueryWrapper.le(GlBehavior::getDate, "2025-08-13 00:00:00");
                List<GlBehavior> glBehaviors = baseMapper.selectList(glBehaviorLambdaQueryWrapper);
                for (GlBehavior glBehavior : glBehaviors) {
                    try {
                        DynamicTableNameHolder.set(String.format("gl_points_%s", key));
                        glPointsService.grantBehaviorPointsDate(glBehavior);
                    }catch (Exception e){
                        log.error("异常1:",e);
                    }finally {
                        DynamicTableNameHolder.remove();
                    }
                }
            }catch (Exception e){
                log.error("异常:",e);
            }finally {
                DynamicTableNameHolder.remove();
            }
        }
    }
}